{
  "eslint.nodePath": "config/eslint-config/node_modules/eslint",
  "prettier.prettierPath": "config/eslint-config/node_modules/prettier",
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.formatOnSave": true,
  "editor.formatOnType": false,
  "editor.formatOnPaste": false,
  "editor.defaultFormatter": "dbaeumer.vscode-eslint",
  "editor.codeActionsOnSave": {
    "source.fixAll": "explicit",
    "source.fixAll.eslint": "explicit"
  },
  "search.followSymlinks": false,
  "search.exclude": {
    "**/node_modules": true,
    "**/.nyc_output": true,
    "**/.rush": true,
    "**/pnpm-lock.yaml": true,
    "**/CHANGELOG.json": true,
    "**/CHANGELOG.md": true,
    "common/changes": true,
    "**/output": true,
    "**/lib": true,
    "**/dist": true,
    "**/coverage": true,
    "common/temp": true
  },
  "eslint.workingDirectories": [
    {
      "mode": "auto"
    }
  ],
  "files.defaultLanguage": "plaintext",
  "files.associations": {
    ".code-workspace": "jsonc",
    ".babelrc": "json",
    ".eslintrc": "jsonc",
    ".eslintrc*.json": "jsonc",
    ".stylelintrc": "jsonc",
    "stylelintrc": "jsonc",
    "*.json": "jsonc",
    "package.json": "json",
    ".htmlhintrc": "jsonc",
    "htmlhintrc": "jsonc",
    "Procfile*": "shellscript",
    "README": "markdown",
    "**/coverage/**/*.*": "plaintext",
    "OWNERS": "yaml",
    "**/pnpm-lock.yaml": "plaintext",
    "**/dist/**": "plaintext",
    "**/dist_*/**": "plaintext",
    "*.map": "plaintext",
    "*.log": "plaintext"
  },
  "files.exclude": {
    "**/.git": true,
    "**/.svn": true,
    "**/.hg": true,
    "**/CVS": true,
    "**/.DS_Store": true,
    "**/Thumbs.db": true,
    "**/.rush": true
  },
  "files.watcherExclude": {
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/node_modules/*/**": true
  },
  "search.useIgnoreFiles": true,
  //
  "editor.rulers": [
    80,
    120
  ],
  "files.eol": "\n",
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  "cSpell.diagnosticLevel": "Warning",
  "eslint.probe": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "eslint.format.enable": true,
  "eslint.lintTask.enable": true,
  "javascript.validate.enable": false,
  "typescript.validate.enable": true,
  "typescript.tsdk": "config/ts-config/node_modules/typescript/lib",
  "typescript.tsserver.maxTsServerMemory": 8192,
  // "typescript.tsserver.experimental.enableProjectDiagnostics": true,
  "typescript.tsserver.watchOptions": {
    "fallbackPolling": "dynamicPriorityPolling",
    "synchronousWatchDirectory": false,
    "watchDirectory": "dynamicPriorityPolling",
    "watchFile": "useFsEventsOnParentDirectory"
  },
  "css.validate": false,
  "scss.validate": false,
  "less.validate": false,
  "emmet.triggerExpansionOnTab": true,
  "[yaml]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[html]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  "[less]": {
    "editor.defaultFormatter": "vscode.css-language-features"
  },
  "[typescript]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[ignore]": {
    "editor.defaultFormatter": "foxundermoon.shell-format"
  },
  "[shellscript]": {
    "editor.defaultFormatter": "foxundermoon.shell-format"
  },
  "[dotenv]": {
    "editor.defaultFormatter": "foxundermoon.shell-format"
  },
  "[svg]": {
    "editor.defaultFormatter": "jock.svg"
  }
}
