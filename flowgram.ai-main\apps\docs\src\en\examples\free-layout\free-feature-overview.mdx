---
outline: false
---


# Best Practices

import { FreeFeatureOverview } from '../../../../components';

<FreeFeatureOverview />

## Installation

```bash
npx @flowgram.ai/create-app@latest free-layout
```

## Source Code

https://github.com/bytedance/flowgram.ai/tree/main/apps/demo-free-layout


## Feature Introduction

<table className="rs-table">
  <tr>
    <td>
      Auto Layout
    </td>
    <td>
      <div className="rs-center">
        <img loading="lazy" src="/free-layout/autolayout.gif"  />
      </div>
    </td>
  </tr>
  <tr>
    <td>
      Snap + Reference Line
    </td>
    <td>
      <div className="rs-center">
        <img loading="lazy" src="/free-layout/snap.gif"  />
      </div>
    </td>
  </tr>
</table>
