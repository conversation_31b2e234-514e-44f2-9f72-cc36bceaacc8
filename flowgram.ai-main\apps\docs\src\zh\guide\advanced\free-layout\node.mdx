# 节点

节点通过 [FlowNodeEntity](/api/core/flow-node-entity.html) 定义


## 节点数据

通过 `node.toJSON()` 可以获取

:::note 基本结构:

- id: `string` 节点唯一标识, 必须保证唯一
- meta: `object` 节点的 ui 配置信息，如自由布局的 `position` 信息放这里
- type: `string | number` 节点类型，会和 `nodeRegistries` 中的 `type` 对应
- data: `object` 节点表单数据, 业务可自定义
- blocks: `array` 节点的分支, 采用 `block` 更贴近 `Gramming` 自由布局布局场景会用在子画布的子节点
- edges: `array` 子画布的边数据

:::

```ts pure
const nodeData: FlowNodeJSON = {
  id: 'xxxx',
  type: 'condition',
  data: {
    title: 'MyCondition',
    desc: 'xxxxx'
  },
}
```

## 节点定义

在自由布局场景，节点定义用于声明节点的初始化位置/大小，端口，表单渲染等, 详细见 [声明节点](guide/getting-started/create-free-layout-simple.html#4-声明节点)

## 当前渲染节点获取

通过 [useNodeRender](api/hooks/use-node-render.html) 获取节点相关方法

```tsx pure
function BaseNode() {
  const { id, type, data, updateData, node } = useNodeRender()
}
```

## 创建节点

- 通过 [WorkflowDocument](/api/core/workflow-document.html) 创建

```ts pure
const ctx = useClientContext()

ctx.document.createWorkflowNode({
  id: 'xxx', // 要保证画布内唯一
  type: 'custom',
  meta: {
    /**
     * 如果不传入，则默认在画布中间创建
     * 如果要通过鼠标位置获取 position (如点击画布任意位置创建节点)，可通过 `ctx.playground.config.getPosFromMouseEvent(mouseEvent)` 转换
     */
    position: { x: 100, y: 100 } //
  },
  data: {}, // 表单相关数据
  blocks: [], // 子画布的节点
  edges: [] // 子画布的边
})

```
- 通过 WorkflowDragService 创建, 见[自由布局基础用法](/examples/free-layout/free-layout-simple.html)

```ts pure
const dragService = useService<WorkflowDragService>(WorkflowDragService);

// 这里的 mouseEvent 会自动转成 画布的 position
dragService.startDragCard(nodeType, mouseEvent, {
  id: 'xxxx',
  data: {}, // 表单相关数据
  blocks: [], // 子画布的节点
  edges: [] // 子画布的边
})

```

## 删除节点

通过 `node.dispose` 删除节点

```tsx pure
function BaseNode({ node }) {
  function onClick() {
    node.dispose()
  }
  return (
    <button onClick={onClick}>Delete</button>
  )
}
```

## 更新节点 data 数据

- 通过 [useNodeRender](/api/hooks/use-node-render.html) 或 [getNodeForm](/api/utils/get-node-form.html) 获取节点的 data 数据

```tsx pure
function BaseNode() {
  const { data, updateData } = useNodeRender();
  // 对应节点的 data 数据
  console.log(data)

  function onChange(e) {
    updateData({
      ...data,
      title: e.target.value
    })
  }
  return <input value={data.title} onChange={onChange}/>
}
```
- 通过 Field 更新表单数据, 详细见 [表单的使用](/guide/advanced/form.html)

```tsx pure

function FormRender() {
  return (
    <Field name="title">
      <Input />
    </Field>
  )
}
```

## 更新节点的 extInfo 数据

extInfo 用于存储 一些 ui 状态, 如果未开启节点引擎，节点的 data 数据会默认存到 extInfo 里

```tsx pure
function BaseNode({ node }) {
  const times = node.getExtInfo()?.times || 0
  function onClick() {
    node.updateExtInfo({ times: times ++ })
  }
  return (
    <div>
      <span>Click Times: {times}</span>
      <button onClick={onClick}>Click</button>
    </div>
  )
}
```

