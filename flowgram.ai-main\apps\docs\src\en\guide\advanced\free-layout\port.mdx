# Port

- [WorkflowNodePortsData](https://github.com/bytedance/flowgram.ai/blob/main/packages/canvas-engine/free-layout-core/src/entity-datas/workflow-node-ports-data.ts) manages all port information for nodes
- [WorkflowPortEntity](https://github.com/bytedance/flowgram.ai/blob/main/packages/canvas-engine/free-layout-core/src/entities/workflow-port-entity.ts) port instance
- [WorkflowPortRender](https://github.com/bytedance/flowgram.ai/blob/main/packages/plugins/free-lines-plugin/src/components/workflow-port-render/index.tsx) port rendering component


## Define Ports

- Static Ports

Add `defaultPorts` to node declaration, such as `{ type: 'input' }`, which will add an input port to the left side of the node

```ts pure title="node-registries.ts"
{
  type: 'start',
  meta: {
    defaultPorts: [{ type: 'output' }, { type: 'input'}]
  },
}
```

- Dynamic Ports

Add `useDynamicPort` to node declaration, when set to true it will look for DOM elements with `data-port-id` and `data-port-type` attributes as ports


```ts pure title="node-registries.ts"
{
  type: 'condition',
  meta: {
    defaultPorts: [{ type: 'input'}]
    useDynamicPort: true
  },
}

```

```tsx pure

/**
* Dynamic ports are found through querySelectorAll('[data-port-id]')
 */
function BaseNode() {
  return (
    <div>
      <div data-port-id="condition-if-0" data-port-type="output"></div>
      <div data-port-id="condition-if-1" data-port-type="output"></div>
      {/* others */}
    </div>
  )
}
```

## Port Rendering

Ports are ultimately rendered through the `WorkflowPortRender` component, supporting custom styles, or businesses can reimplement this component based on the source code, see [Free Layout Best Practices - Node Rendering](https://github.com/bytedance/flowgram.ai/blob/main/apps/demo-free-layout/src/components/base-node/node-wrapper.tsx)

### Custom Port Colors

You can customize port colors by passing color props to `WorkflowPortRender`:

- `primaryColor` - Active state color (when linked or hovered)
- `secondaryColor` - Default state color
- `errorColor` - Error state color
- `backgroundColor` - Background color

```tsx pure

import { WorkflowPortRender, useNodeRender } from '@flowgram.ai/free-layout-editor';

function BaseNode() {
  const { ports } = useNodeRender();
  return (
    <div>
      <div data-port-id="condition-if-0" data-port-type="output"></div>
      <div data-port-id="condition-if-1" data-port-type="output"></div>
      {ports.map((p) => (
        <WorkflowPortRender
          key={p.id}
          entity={p}
          className="xxx"
          style={{ /* custom style */}}
          // Custom port colors
          primaryColor="#4d53e8"        // Active state color (linked/hovered)
          secondaryColor="#9197f1"      // Default state color
          errorColor="#ff4444"          // Error state color
          backgroundColor="#ffffff"     // Background color
        />
      ))}
    </div>
  )
}
```

## Get Port Data

```ts pure
const ports = node.getData(WorkflowNodePortsData)

console.log(ports.inputPorts) // Get all input ports of the current node
console.log(ports.outputPorts) // Get all output ports of the current node

console.log(ports.inputPorts.map(port => port.availableLines)) // Find connected lines through ports

ports.updateDynamicPorts() // When dynamic ports modify DOM structure or position, you can manually refresh port positions through this method (DOM rendering has delay, best to execute in useEffect or setTimeout)
```


