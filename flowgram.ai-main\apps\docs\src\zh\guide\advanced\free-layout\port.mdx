# 端口

- [WorkflowNodePortsData](https://github.com/bytedance/flowgram.ai/blob/main/packages/canvas-engine/free-layout-core/src/entity-datas/workflow-node-ports-data.ts) 管理节点的所有端口信息
- [WorkflowPortEntity](https://github.com/bytedance/flowgram.ai/blob/main/packages/canvas-engine/free-layout-core/src/entities/workflow-port-entity.ts) 端口实例
- [WorkflowPortRender](https://github.com/bytedance/flowgram.ai/blob/main/packages/plugins/free-lines-plugin/src/components/workflow-port-render/index.tsx) 端口渲染组件


## 定义端口

- 静态端口

节点声明添加 `defaultPorts` , 如 `{ type: 'input' }`, 则会在节点左侧加入输入端口

```ts pure title="node-registries.ts"
{
  type: 'start',
  meta: {
    defaultPorts: [{ type: 'output' }, { type: 'input'}]
  },
}
```

- 动态端口

节点声明添加 `useDynamicPort` , 当设置为 true 则会到节点dom 上寻找 data-port-id 和 data-port-type 属性的 dom 作为端口


```ts pure title="node-registries.ts"
{
  type: 'condition',
  meta: {
    defaultPorts: [{ type: 'input'}]
    useDynamicPort: true
  },
}

```

```tsx pure

/**
*  动态端口通过 querySelectorAll('[data-port-id]') 查找端口位置
 */
function BaseNode() {
  return (
    <div>
      <div data-port-id="condition-if-0" data-port-type="output"></div>
      <div data-port-id="condition-if-1" data-port-type="output"></div>
      {/* others */}
    </div>
  )
}
```

## 端口渲染

端口最终通过 `WorkflowPortRender` 组件渲染，支持自定义 style, 或者业务基于源码重新实现该组件, 参考 [自由布局最佳实践 - 节点渲染](https://github.com/bytedance/flowgram.ai/blob/main/apps/demo-free-layout/src/components/base-node/node-wrapper.tsx)

### 自定义端口颜色

可以通过向 `WorkflowPortRender` 传递颜色 props 来自定义端口颜色：

- `primaryColor` - 激活状态颜色（linked/hovered）
- `secondaryColor` - 默认状态颜色
- `errorColor` - 错误状态颜色
- `backgroundColor` - 背景颜色

```tsx pure

import { WorkflowPortRender, useNodeRender } from '@flowgram.ai/free-layout-editor';

function BaseNode() {
  const { ports } = useNodeRender();
  return (
    <div>
      <div data-port-id="condition-if-0" data-port-type="output"></div>
      <div data-port-id="condition-if-1" data-port-type="output"></div>
      {ports.map((p) => (
        <WorkflowPortRender
          key={p.id}
          entity={p}
          className="xxx"
          style={{ /* custom style */}}
          // 自定义端口颜色
          primaryColor="#4d53e8"        // 激活状态颜色（linked/hovered）
          secondaryColor="#9197f1"      // 默认状态颜色
          errorColor="#ff4444"          // 错误状态颜色
          backgroundColor="#ffffff"     // 背景颜色
        />
      ))}
    </div>
  )
}
```

## 获取端口数据

```ts pure
const ports = node.getData(WorkflowNodePortsData)

console.log(ports.inputPorts) // 获取当前节点的所有输入端口
console.log(ports.outputPorts) // 获取当前节点的所有输出端口

console.log(ports.inputPorts.map(port => port.availableLines)) // 通过端口找到连接的线条

ports.updateDynamicPorts() // 当动态端口修改了 dom 结构或位置，可以通过该方法手动刷新端口位置(dom 渲染有延迟，最好在 useEffect 或者 setTimeout 执行)
```


