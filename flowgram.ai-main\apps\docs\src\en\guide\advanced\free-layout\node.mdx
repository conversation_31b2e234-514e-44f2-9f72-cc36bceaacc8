# Node

Nodes are defined through [FlowNodeEntity](/api/core/flow-node-entity.html)

## Node Data

Can be obtained through `node.toJSON()`

:::note Basic Structure:

- id: `string` Unique identifier for the node, must be unique
- meta: `object` Node's UI configuration information, such as `position` information for free layout
- type: `string | number` Node type, corresponds to `type` in `nodeRegistries`
- data: `object` Node form data, can be customized by business
- blocks: `array` Node branches, using `block` is more suitable for `Gramming` free layout scenarios, used in sub-nodes of sub-canvas
- edges: `array` Edge data of sub-canvas

:::

```ts pure
const nodeData: FlowNodeJSON = {
  id: 'xxxx',
  type: 'condition',
  data: {
    title: 'MyCondition',
    desc: 'xxxxx'
  },
}
```

## Node Definition

In free layout scenarios, node definition is used to declare node's initial position/size, ports, form rendering, etc. For details, see [Declare Node](guide/getting-started/create-free-layout-simple.html#4-declare-nodes)

## Get Current Rendering Node

Get node-related methods through [useNodeRender](api/hooks/use-node-render.html)

```tsx pure
function BaseNode() {
  const { id, type, data, updateData, node } = useNodeRender()
}
```

## Create Node

- Through [WorkflowDocument](/api/core/workflow-document.html)

```ts pure
const ctx = useClientContext()

ctx.document.createWorkflowNode({
  id: 'xxx', // Must be unique within the canvas
  type: 'custom',
  meta: {
    /**
     * If not provided, defaults to creating in the center of the canvas
     * To get position from mouse position (e.g., creating node by clicking anywhere on canvas),
     * convert using `ctx.playground.config.getPosFromMouseEvent(mouseEvent)`
     */
    position: { x: 100, y: 100 } //
  },
  data: {}, // Form-related data
  blocks: [], // Sub-canvas nodes
  edges: [] // Sub-canvas edges
})

```
- Through WorkflowDragService, see [Free Layout Basic Usage](/examples/free-layout/free-layout-simple.html)

```ts pure
const dragService = useService<WorkflowDragService>(WorkflowDragService);

// mouseEvent here will automatically convert to canvas position
dragService.startDragCard(nodeType, mouseEvent, {
  id: 'xxxx',
  data: {}, // Form-related data
  blocks: [], // Sub-canvas nodes
  edges: [] // Sub-canvas edges
})

```

## Delete Node

Delete node through `node.dispose`

```tsx pure
function BaseNode({ node }) {
  function onClick() {
    node.dispose()
  }
  return (
    <button onClick={onClick}>Delete</button>
  )
}
```

## Update Node Data

- Get node's data through [useNodeRender](/api/hooks/use-node-render.html) or [getNodeForm](/api/utils/get-node-form.html)

```tsx pure
function BaseNode() {
  const { data, updateData } = useNodeRender();
  // Corresponds to node's data
  console.log(data)

  function onChange(e) {
    updateData({
      ...data,
      title: e.target.value
    })
  }
  return <input value={data.title} onChange={onChange}/>
}

```
- Update form data through Field, see details in [Form Usage](/guide/advanced/form.html)

```tsx pure

function FormRender() {
  return (
    <Field name="title">
      <Input />
    </Field>
  )
}
```

## Update Node's extInfo Data

extInfo is used to store UI states, if node engine is not enabled, node's data will be stored in extInfo by default

```tsx pure
function BaseNode({ node }) {
  const times = node.getExtInfo()?.times || 0
  function onClick() {
    node.updateExtInfo({ times: times ++ })
  }
  return (
    <div>
      <span>Click Times: {times}</span>
      <button onClick={onClick}>Click</button>
    </div>
  )
}
```

