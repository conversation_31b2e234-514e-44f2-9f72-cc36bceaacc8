// API 服务层 - 连接 Eino 后端
const API_BASE_URL = 'http://localhost:8082/api';

export interface AIApplication {
  id: string;
  name: string;
  description: string;
  avatar: string;
  isPublished: boolean;
  lastEdited: string; // ISO string
}

export interface CreateApplicationRequest {
  name: string;
  description: string;
  avatar: string;
}

export interface ChatMessage {
  id: number;
  type: 'user' | 'assistant';
  content: string;
}

export interface ChatRequest {
  applicationId: string;
  message: string;
  history: ChatMessage[];
}

class ApiService {
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: 'Network error' }));
      throw new Error(error.error || `HTTP ${response.status}`);
    }

    return response.json();
  }

  // 获取应用列表
  async getApplications(): Promise<AIApplication[]> {
    const response = await this.request<{ applications: AIApplication[] }>('/applications');
    return response.applications.map(app => ({
      ...app,
      lastEdited: app.lastEdited, // 保持字符串格式，前端会转换
    }));
  }

  // 创建应用
  async createApplication(data: CreateApplicationRequest): Promise<AIApplication> {
    const response = await this.request<{ application: AIApplication }>('/applications', {
      method: 'POST',
      body: JSON.stringify(data),
    });
    return response.application;
  }

  // 获取应用详情
  async getApplication(id: string): Promise<AIApplication> {
    const response = await this.request<{ application: AIApplication }>(`/applications/${id}`);
    return response.application;
  }

  // 聊天接口
  async chat(request: ChatRequest): Promise<ChatMessage> {
    const response = await this.request<{ message: ChatMessage }>('/chat', {
      method: 'POST',
      body: JSON.stringify(request),
    });
    return response.message;
  }

  // 流式聊天接口（未来扩展）
  async streamChat(request: ChatRequest): Promise<ReadableStream<ChatMessage>> {
    const response = await fetch(`${API_BASE_URL}/chat/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    return new ReadableStream({
      start(controller) {
        const reader = response.body?.getReader();
        if (!reader) {
          controller.close();
          return;
        }

        function pump(): Promise<void> {
          return reader.read().then(({ done, value }) => {
            if (done) {
              controller.close();
              return;
            }

            // 解析 SSE 数据
            const chunk = new TextDecoder().decode(value);
            const lines = chunk.split('\n');
            
            for (const line of lines) {
              if (line.startsWith('data: ')) {
                try {
                  const data = JSON.parse(line.slice(6));
                  controller.enqueue(data);
                } catch (e) {
                  console.error('Failed to parse SSE data:', e);
                }
              }
            }

            return pump();
          });
        }

        return pump();
      },
    });
  }
}

export const apiService = new ApiService();

// 兼容性适配器 - 让现有的 hooks 无需修改
export const adaptApplicationsForHooks = (apps: AIApplication[]) => {
  return apps.map(app => ({
    ...app,
    lastEdited: new Date(app.lastEdited), // 转换为 Date 对象
  }));
};

// 错误处理工具
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// 连接状态检查
export const checkBackendConnection = async (): Promise<boolean> => {
  try {
    await fetch(`${API_BASE_URL}/health`, { method: 'GET' });
    return true;
  } catch {
    return false;
  }
};

// 开发模式下的模拟数据切换
export const isDevelopmentMode = () => {
  return process.env.NODE_ENV === 'development' && !process.env.REACT_APP_USE_EINO_BACKEND;
};
