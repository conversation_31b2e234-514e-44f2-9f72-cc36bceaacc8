{"name": "@flowgram.ai/create-app", "version": "0.1.8", "description": "A CLI tool to create demo projects", "bin": {"create-app": "./bin/index.js"}, "type": "module", "files": ["bin", "src", "dist"], "scripts": {"build": "tsup src/index.ts --format esm,cjs --dts --out-dir dist", "start": "node bin/index.js"}, "dependencies": {"fs-extra": "^9.1.0", "commander": "^11.0.0", "chalk": "^5.3.0", "download": "8.0.0", "tar": "7.4.3", "inquirer": "^9.2.7"}, "devDependencies": {"@types/download": "8.0.5", "@types/fs-extra": "11.0.4", "@types/node": "^18", "@types/inquirer": "9.0.7", "tsup": "^8.0.1", "eslint": "^8.54.0", "@typescript-eslint/parser": "^6.10.0", "typescript": "^5.0.4"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}}