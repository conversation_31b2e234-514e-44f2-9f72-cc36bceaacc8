# Creating a Fixed Layout Canvas

This example can be installed using `npx @flowgram.ai/create-app@latest fixed-layout-simple`. For complete code and demo, see:

<div className="rs-tip">
  <a className="rs-link" href="/en/examples/fixed-layout/fixed-layout-simple.html">
    Fixed Layout Basic Usage
  </a>
</div>

File structure:

```
- hooks
  - use-editor-props.ts # Canvas configuration
- components
  - base-node.tsx # Node rendering
  - tools.tsx # Canvas toolbar
- initial-data.ts # Initialization data
- node-registries.ts # Node configuration
- app.tsx # Canvas entry
```

### 1. Canvas Entry

- `FixedLayoutEditorProvider`: Canvas configurator that generates react-context internally for child components to consume
- `EditorRenderer`: The final rendered canvas that can be wrapped under other components for customizing canvas position

```tsx pure title="app.tsx"
import {
  FixedLayoutEditorProvider,
  EditorRenderer,
} from '@flowgram.ai/fixed-layout-editor';

import '@flowgram.ai/fixed-layout-editor/index.css'; // Load styles

import { useEditorProps } from './hooks/use-editor-props' // Detailed canvas props configuration
import { Tools } from './components/tools' // Canvas tools

function App() {
  const editorProps = useEditorProps()
  return (
    <FixedLayoutEditorProvider {...editorProps}>
      <EditorRenderer className="demo-editor" />
      <Tools />
    </FixedLayoutEditorProvider>
  );
}
```

### 2. Configure Canvas

Canvas configuration is declarative, providing configurations for data, rendering, events, and plugins.

```tsx pure title="hooks/use-editor-props.tsx"
import { useMemo } from 'react';
import { type FixedLayoutProps } from '@flowgram.ai/fixed-layout-editor';
import { defaultFixedSemiMaterials } from '@flowgram.ai/fixed-semi-materials';
import { createMinimapPlugin } from '@flowgram.ai/minimap-plugin';

import { initialData } from './initial-data' // Initialization data
import { nodeRegistries } from './node-registries' // Node declaration configuration
import { BaseNode } from './base-node' // Node rendering

export function useEditorProps(
): FixedLayoutProps {
  return useMemo<FixedLayoutProps>(
    () => ({
      /**
       * Initialization data
       */
      initialData,
      /**
       * Canvas node definitions
       */
      nodeRegistries,
      /**
       * UI components can be customized through keys. Here, a set of semi components is provided for quick validation.
       * For deep customization, refer to:
       * https://github.com/bytedance/flowgram.ai/blob/main/packages/materials/fixed-semi-materials/src/components/index.tsx
       */
      materials: {
        components: {
          ...defaultFixedSemiMaterials,
          // [FlowRendererKey.ADDER]: NodeAdder,
          // [FlowRendererKey.BRANCH_ADDER]: BranchAdder,
        },
        renderDefaultNode: BaseNode, // Node rendering component
      },
      /**
       * Node engine for rendering node forms
       */
      nodeEngine: {
        enable: true,
      },
      /**
       * Canvas history record for controlling redo/undo
       */
      history: {
        enable: true,
        enableChangeNode: true, // Monitor node form data changes
      },
      /**
       * Canvas initialization callback
       */
      onInit: ctx => {
        // For dynamic data loading, use the following method asynchronously
        // ctx.docuemnt.fromJSON(initialData)
      },
      /**
       * Callback when canvas first render completes
       */
      onAllLayersRendered: (ctx) => {},
      /**
       * Canvas disposal callback
       */
      onDispose: () => { },
      plugins: () => [
        /**
         * Minimap plugin
         */
        createMinimapPlugin({}),
      ],
    }),
    [],
  );
}
```

### 3. Configure Data

Canvas document data uses a tree structure that supports nesting.

:::note Document Data Basic Structure:

- nodes `array` Node list, supports nesting

:::

:::note Node Data Basic Structure:

- id: `string` Unique node identifier, must be unique
- meta: `object` Node UI configuration information, such as position information for free layout
- type: `string | number` Node type, corresponds to `type` in `nodeRegistries`
- data: `object` Node form data
- blocks: `array` Node branches, using `block` is closer to `Gramming`

:::

```tsx pure title="initial-data.tsx"
import { FlowDocumentJSON } from '@flowgram.ai/fixed-layout-editor';

/**
 * Configure flow data, data is in blocks nested format
 */
export const initialData: FlowDocumentJSON = {
  nodes: [
    // Start node
    {
      id: 'start_0',
      type: 'start',
      data: {
        title: 'Start',
        content: 'start content'
      },
      blocks: [],
    },
    // Condition node
    {
      id: 'condition_0',
      type: 'condition',
      data: {
        title: 'Condition'
      },
      blocks: [
        {
          id: 'branch_0',
          type: 'block',
          data: {
            title: 'Branch 0',
            content: 'branch 1 content'
          },
          blocks: [
            {
              id: 'custom_0',
              type: 'custom',
              data: {
                title: 'Custom',
                content: 'custom content'
              },
            },
          ],
        },
        {
          id: 'branch_1',
          type: 'block',
          data: {
            title: 'Branch 1',
            content: 'branch 1 content'
          },
          blocks: [],
        },
      ],
    },
    // End node
    {
      id: 'end_0',
      type: 'end',
      data: {
        title: 'End',
        content: 'end content'
      },
    },
  ],
};
```

### 4. Declare Nodes

Node declaration can be used to determine node types and rendering methods.

```tsx pure title="node-registries.tsx"
import { FlowNodeRegistry, ValidateTrigger } from '@flowgram.ai/fixed-layout-editor';

/**
 * Custom node registration
 */
export const nodeRegistries: FlowNodeRegistry[] = [
  {
    /**
     * Custom node type
     */
    type: 'condition',
    /**
     * Custom node extensions:
     *  - loop: Extend as loop node
     *  - start: Extend as start node
     *  - dynamicSplit: Extend as branch node
     *  - end: Extend as end node
     *  - tryCatch: Extend as tryCatch node
     *  - default: Extend as normal node (default)
     */
    extend: 'dynamicSplit',
    /**
     * Node configuration information
     */
    meta: {
      // isStart: false, // Whether it's a start node
      // isNodeEnd: false, // Whether it's an end node, no nodes can be added after end node
      // draggable: false, // Whether draggable, start and end nodes cannot be dragged
      // selectable: false, // Triggers and start nodes cannot be box-selected
      // deleteDisable: true, // Disable deletion
      // copyDisable: true, // Disable copying
      // addDisable: true, // Disable adding
    },
    /**
     * Configure node form validation and rendering
     * Note: validate uses data and rendering separation to ensure node validation even without rendering
     */
    formMeta: {
      validateTrigger: ValidateTrigger.onChange,
      validate: {
        title: ({ value }) => (value ? undefined : 'Title is required'),
      },
      /**
       * Render form
       */
      render: () => (
       <>
          <Field name="title">
            {({ field }) => <div className="demo-free-node-title">{field.value}</div>}
          </Field>
          <Field name="content">
            {({ field }) => <input onChange={field.onChange} value={field.value}/>}
          </Field>
        </>
      )
    },
  },
];
```

### 5. Render Nodes

Node rendering is used to add styles, events, and form rendering positions.

```tsx pure title="components/base-node.tsx"
import { useNodeRender } from '@flowgram.ai/fixed-layout-editor';

export const BaseNode = () => {
  /**
   * Provides node rendering related methods
   */
  const nodeRender = useNodeRender();
  /**
   * Form can only be used when node engine is enabled
   */
  const form = nodeRender.form;

  return (
    <div
      className="demo-fixed-node"
      onMouseEnter={nodeRender.onMouseEnter}
      onMouseLeave={nodeRender.onMouseLeave}
      onMouseDown={e => {
        // Trigger drag
        nodeRender.startDrag(e);
        e.stopPropagation();
      }}
      style={{
        // BlockOrderIcon represents the first node of a branch, BlockIcon represents the header node of the entire condition
        ...(nodeRender.isBlockOrderIcon || nodeRender.isBlockIcon ? { width: 260 } : {}),
        outline: form?.state.invalid ? '1px solid red' : 'none', // Red border for form validation errors
      }}
    >
      {
        // Form rendering generated through formMeta
        form?.render()
      }
    </div>
  );
};
```

### 6. Add Tools

Tools are mainly used to control canvas zooming and other operations. Tools are collected in `usePlaygroundTools`, while `useClientContext` is used to get canvas context, which contains core modules like `history`.

```tsx pure title="components/tools.tsx"
import { useEffect, useState } from 'react'
import { usePlaygroundTools, useClientContext } from '@flowgram.ai/fixed-layout-editor';

export function Tools() {
  const { history } = useClientContext();
  const tools = usePlaygroundTools();
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);

  useEffect(() => {
    const disposable = history.undoRedoService.onChange(() => {
      setCanUndo(history.canUndo());
      setCanRedo(history.canRedo());
    });
    return () => disposable.dispose();
  }, [history]);

  return <div style={{ position: 'absolute', zIndex: 10, bottom: 16, left: 16, display: 'flex', gap: 8 }}>
    <button onClick={() => tools.zoomin()}>ZoomIn</button>
    <button onClick={() => tools.zoomout()}>ZoomOut</button>
    <button onClick={() => tools.fitView()}>Fitview</button>
    <button onClick={() => tools.changeLayout()}>ChangeLayout</button>
    <button onClick={() => history.undo()} disabled={!canUndo}>Undo</button>
    <button onClick={() => history.redo()} disabled={!canRedo}>Redo</button>
    <span>{Math.floor(tools.zoom * 100)}%</span>
  </div>
}
```

### 7. Result

import { FixedLayoutSimple } from '../../../../components';

<div style={{ position: 'relative', width: '100%', height: '600px'}}>
  <FixedLayoutSimple />
</div>
