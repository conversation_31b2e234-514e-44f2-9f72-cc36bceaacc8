# Basic Config

## root

- Type: `string`
- Default: `docs`

Specifies the document root directory. For example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  root: 'docs',
});
```

This config supports both relative and absolute paths, with relative paths being relative to the current working directory(cwd).

Of course, in addition to specifying the document root directory through the config file, you can also specify it through command line parameters, such as:

```bash
rspress dev docs
rspress build docs
```

## base

- Type: `string`
- Default: `/`

Deployment base path. For example, if you plan to deploy your site to `https://foo.github.io/bar/`, then you should set `base` to `"/bar/"`:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  base: '/bar/',
});
```

## title

- Type: `string`
- Default: `"Rspress"`

Site title. This parameter will be used as the title of the HTML page. For example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  title: 'My Site',
});
```

## description

- Type: `string`
- Default: `""`

Site description. This will be used as the description of the HTML page. For example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  description: 'My Site Description',
});
```

## icon

- Type: `string`
- Default: `""`

Site icon. This path will be used as the icon path for the HTML page. For example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  icon: '/favicon.ico',
});
```

The framework will find your icon in the `public` directory, of course you can also set it to a CDN address.

## logo \{#logo-1}

- Type: `string | { dark: string; light: string }`
- Default: `""`

Site logo. This path will be used as the logo path in the upper left corner of the navbar. For example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  logo: '/logo.png',
});
```

The framework will find your icon in the `public` directory, you can also set it to a CDN address.

Of course you can set different logos for dark/light mode:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  logo: {
    dark: '/logo-dark.png',
    light: '/logo-light.png',
  },
});
```

## logoText

- Type: `string`
- Default: `""`

Site logo Text. This text will be used as the logo text in the upper left corner of the navbar. For example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  logoText: 'rspress',
});
```

## outDir

- Type: `string`
- Default: `doc_build`

Custom output directory for built sites. for example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  outDir: 'doc_build',
});
```

## locales

- Type: `Locale[]`

```ts
export interface Locale {
  lang: string;
  label: string;
  title?: string;
  description?: string;
}
```

I18n config of the site. for example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  locales: [
    {
      lang: 'en-US',
      label: 'English',
      title: 'Doc Tools',
      description: 'Doc Tools',
    },
    {
      lang: 'zh-CN',
      label: '简体中文',
      title: '文档框架',
      description: '文档框架',
    },
  ],
});
```

## head

- Type: `string` | `[string, Record<string, string>]` | `(route) => string | [string, Record<string, string>] | undefined`
- Can be appended per page via [frontmatter](./config-frontmatter.mdx#head)

Additional elements to render in the `<head>` tag in the page HTML.

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  // ... other user config
  head: [
    '<meta name="author" content="John Doe">',
    // or
    ['meta', { name: 'author', content: 'John Doe' }],
    // [htmlTag, { attrName: attrValue, attrName2: attrValue2 }]
    // or
    (route) => {
      if (route.routePath.startsWith('/jane/')) return "<meta name='author' content='Jane Doe'>";
      if (route.routePath.startsWith('/john/')) return ['meta', { name: 'author', content: 'John Doe' }];
      \\ or even skip returning anything
      return undefined;
    }
  ]
});
```

## mediumZoom

- Type: `boolean` | `{ selector?: string }`
- Default: `true`

Whether to enable the image zoom function. It is enabled by default, you can disable it by setting `mediumZoom` to `false`.

> The bottom layer is implemented using the [medium-zoom](https://github.com/francoischalifour/medium-zoom) library.

Example usage:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  // Turn off image zoom
  mediumZoom: false,
  // Configure the CSS selector to customize the picture to be zoomed, the default is '.rspress-doc img'
  mediumZoom: {
    selector: '.rspress-doc img',
  },
});
```

## search

- Type: `{ searchHooks: string; versioned: boolean; }`

### searchHooks

You can add search runtime hooks logic through the `searchHooks` parameter, for example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';
import path from 'path';

export default defineConfig({
  search: {
    searchHooks: path.join(__dirname, 'searchHooks.ts'),
  },
});
```

For specific hook logic, you can read [Customize Search Functions](/guide/advanced/custom-search).

### versioned

If you are using `multiVersion`, the `versioned` parameter allows you to create a separate search index for each version of your documentation.
When enabled, the search will only query the index corresponding to the currently selected version.

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  search: {
    versioned: true,
  },
});
```

## globalUIComponents

- Type: `(string | [string, object])[]`
- Default: `[]`

You can register global UI components through the `globalUIComponents` parameter, for example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';
import path from 'path';

export default defineConfig({
  globalUIComponents: [path.join(__dirname, 'components', 'MyComponent.tsx')],
});
```

The item of `globalUIComponents` can be a string, which is the path of the component file, or an array, the first item is the path of the component file, and the second item is the component props, for example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  globalUIComponents: [
    [
      path.join(__dirname, 'components', 'MyComponent.tsx'),
      {
        foo: 'bar',
      },
    ],
  ],
});
```

## multiVersion

- Type: `{ default: string; versions: string[] }`

You can enable multi-version support through the `multiVersion` parameter, for example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  multiVersion: {
    default: 'v1',
    versions: ['v1', 'v2'],
  },
});
```

The `default` parameter is the default version, and the `versions` parameter is the version list.

## route

- Type: `Object`

Custom route config.

### route.include

- Type: `string[]`
- Default: `[]`

Add some extra files in the route. By default, only the files in the document root directory will be included in the route. If you want to add some extra files to the route, you can use this option. For example:

```js
import { defineConfig } from 'rspress/config';

export default defineConfig({
  route: {
    include: ['other-dir/**/*.{md,mdx}'],
  },
});
```

> Note: The strings in the array support glob patterns, the glob expression should be based on the `root` directory of the document, with the corresponding extensions suffix.

:::note

We recommend using [addPages hook](plugin/system/plugin-api.html#addpages) in a custom Rspress plugin to add some additional files to the route, so that the page route and file path/content can be specified more flexibly and reasonably.

:::

### route.exclude

- Type: `string[]`
- Default: `[]`

Exclude some files from the route. For example:

```js
import { defineConfig } from 'rspress/config';

export default defineConfig({
  route: {
    exclude: ['custom.tsx', 'component/**/*'],
  },
});
```

> Note: The strings in the array support glob patterns, the glob expression should be based on the `root` directory of the document.

### route.extensions

- Type: `string[]`
- Default: `[]`

The extensions of the files that will be included in the route. By default, Rspress will include all `'js'`, `'jsx'`, `'ts'`, `'tsx'`, `'md'`, `'mdx'` files in the route. If you want to customize the extensions, you can use this option. For example:

```js
import { defineConfig } from 'rspress/config';

export default defineConfig({
  route: {
    extensions: ['.jsx', '.md', '.mdx'],
  },
});
```

### route.cleanUrls

- Type: `Boolean`
- Default: `false`

Generate url without suffix when `cleanUrls` is `true` for shorter url link.

```js
import { defineConfig } from 'rspress/config';

export default defineConfig({
  route: {
    cleanUrls: true,
  },
});
```

## ssg

- Type: `boolean | { strict?: boolean }`
- Default: `true`

Determines whether to enable Static Site Generation. It is enabled by default, but you can disable it by setting `ssg` to `false`.

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  ssg: false,
});
```

If SSG fails, it will fallback to CSR by default. You can set `ssg` to `{ strict: true }` to strictly require SSG to succeed, otherwise an error will be thrown.

```ts title="rspress.config.ts"
export default {
  ssg: {
    strict: true,
  },
};
```

## replaceRules

- Type: `{ search: string | RegExp; replace: string; }[]`
- Default: `[]`

You can set text replacement rules for the entire site through `replaceRules`. The rules will apply to everything including `_meta.json` files, frontmatter configurations, and document content and titles.

```ts title="rspress.config.ts"
export default {
  replaceRules: [
    {
      search: /foo/g,
      replace: 'bar',
    },
  ],
};
```
