# WorkflowLinesManager (free)

自由布局线条管理, 目前挂在自由布局 document 下边

[> API Detail](https://flowgram.ai/auto-docs/free-layout-core/classes/WorkflowLinesManager.html)

```
import { useClientContext } from '@flowgram.ai/free-layout-editor'

const ctx = useClientContext();
console.log(ctx.document.linesManager)
```

## getAllLines

获取所有线条的实体

```ts pure
const allLines = ctx.document.linesManager.getAllLines()

```

## createLine

创建线条
```ts pure
// from和 to 为对应要连线的节点id， fromPort, toPort 为 端口 id, 如果节点为单个端口可以不指定
const line = ctx.document.linesManager.createLine({ from, to, fromPort, toPort })
```

## toJSON

导出线条数据

```ts pure
const json = ctx.document.linesManager.toJSON()
```

## onAvailableLinesChange

监听所有线条的连线变化

```ts pure
import { useEffect } from 'react'
import { useClientContext, useRefresh } from '@flowgram.ai/free-layout-editor'


function SomeReact() {
  const refresh = useRefresh()
  const linesManager = useClientContext().document.linesManager
  useEffect(() => {
      const dispose = linesManager.onAvailableLinesChange(() => refresh())
      return () => dispose.dispose()
  }, [])
  console.log(ctx.document.linesManager.getAllLines())
}
```

## 自定义箭头渲染器

WorkflowLinesManager 支持通过渲染器注册表自定义箭头样式。详细使用方法请参考 [线条配置指南](/zh/guide/advanced/free-layout/line#4自定义箭头渲染器) 文档。

```tsx
// 简单示例：注册自定义箭头
const editorProps = {
  materials: {
    components: {
      'arrow-renderer': MyCustomArrow,
    },
  },
};
```
