/**
 * Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

.workflow-group-render {
  border-radius: 8px;
  pointer-events: none;
}

.workflow-group-header {
  height: 30px;
  width: fit-content;
  background-color: #fefce8;
  border: 1px solid #facc15;
  border-radius: 8px;
  padding-right: 8px;
  pointer-events: auto;
}

.workflow-group-ungroup {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 30px;
  width: 30px;
  position: absolute;
  top: 35px;
  right: 0;
  border-radius: 8px;
  cursor: pointer;
  pointer-events: auto;
}

.workflow-group-ungroup .ant-btn {
  color: #9ca3af;
}

.workflow-group-ungroup:hover .ant-btn {
  color: #374151;
}

.workflow-group-background {
  position: absolute;
  pointer-events: none;
  top: 0;
  background-color: #fddf4729;
  border: 1px solid #fde047;
  border-radius: 8px;
  width: 100%;
}

.workflow-group-render.selected .workflow-group-background {
  border: 1px solid #facc15;
}

.workflow-group-tools {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 4px;
  height: 100%;
  cursor: move;
  color: oklch(44.6% 0.043 257.281);
  font-size: 14px;
}
.workflow-group-title {
  margin: 0;
  max-width: 242px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
}

.workflow-group-tools-drag {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 4px;
}

.workflow-group-color {
  width: 16px;
  height: 16px;
  border-radius: 8px;
  background-color: #fde047;
  margin-left: 4px;
  cursor: pointer;
}

.workflow-group-title-input {
  width: 242px;
  border: none;
  color: #374151;
}

.workflow-group-color-palette {
  display: grid;
  grid-template-columns: repeat(6, 24px);
  gap: 12px;
  margin: 8px;
  padding: 8px;
}

.workflow-group-color-item {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #fde047;
  cursor: pointer;
  border: 3px solid;
}
