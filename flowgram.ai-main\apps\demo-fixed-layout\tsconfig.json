{
  "extends": "@flowgram.ai/ts-config/tsconfig.flow.path.json",
  "compilerOptions": {
    "rootDir": "./src",
    "outDir": "./dist",
    "experimentalDecorators": true,
    "target": "es2020",
    "module": "esnext",
    "strictPropertyInitialization": false,
    "strict": true,
    "esModuleInterop": true,
    "moduleResolution": "node",
    "skipLibCheck": true,
    "noUnusedLocals": true,
    "noImplicitAny": true,
    "allowJs": true,
    "resolveJsonModule": true,
    "types": ["node"],
    "jsx": "react-jsx",
    "lib": ["es6", "dom", "es2020", "es2019.Array"]
  },
  "include": ["./src"],
}
