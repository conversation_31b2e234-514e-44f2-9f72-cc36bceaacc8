# Variable Basics

## What is a Variable?

Imagine you're building a complex Lego model where each module needs to connect precisely. In the world of Workflows, **variables** play a similar role as "connectors." They are the "messengers" used to pass information between different nodes.

Simply put, a variable is a named container where you can store various things, such as user input, calculation results, or data retrieved from somewhere.

A variable typically consists of three parts:

- **Name (Unique Identifier)**: Like your name, it allows everyone to find this variable accurately. For example, `userName`, `orderId`.
- **Value**: The content inside the container. It can be a number `123`, text `"Hello Flowgram!"`, or a switch state `true` / `false`.
- **Type**: Specifies what kind of things this container can hold. For instance, some can only hold numbers, while others can only hold text.

---

For example, in an "Intelligent Q&A" flow:

<div style={{display: 'flex', gap: '20px'}}>
  <img style={{width: "50%"}} loading="lazy" src="/variable/variable-biz-context-websearch-llm.png" />
  <div>
    <p style={{marginTop: 10}}>1. **`WebSearch` Node**: Responsible for searching the web and putting the found knowledge (e.g., the answer to "What's the weather like today?") into a variable named `natural_language_desc`.</p>
    <p style={{marginTop: 5}}>2. **`LLM` Node**: It takes the `natural_language_desc` "messenger," reads its content, and then answers the user in a more natural and friendly way.</p>
    <p style={{marginTop: 5}}>3. In this process, the type of `natural_language_desc` is "string" because it contains text content.</p>
  </div>
</div>

## Why Do You Need a Variable Engine?

As the complexity of workflows increases, so do the number and management difficulty of variables.

To address this challenge, Flowgram provides a powerful **Variable Engine**.

It acts like a professional "data steward," systematically managing all variables to ensure the clarity and stability of the data flow.

Enabling the Variable Engine will bring you the following core advantages:

<div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "25px" }}>
  <div style={{ gridColumn: "span 2" }}>
    <b>Scope Constraints: Precise Data Access Control</b>
    <p className="rs-tip">The Variable Engine can precisely control the effective range (i.e., scope) of each variable. Like having specific keys for different rooms, it ensures that variables are only accessed within the intended nodes, effectively preventing data pollution and unexpected logical errors.</p>
    <div style={{display: "flex", gap: "25px"}}>
      <div>
        <img loading="lazy" src="/variable/variable-scope-feature-1.png" />
        <p style={{marginTop: '10px'}}>The `query` variable defined in the `Start` node can be easily accessed by the subsequent `LLM` and `End` nodes.</p>
      </div>
      <div>
        <img loading="lazy" src="/variable/variable-scope-feature-2.png" />
        <p style={{marginTop: '10px'}}>The `LLM` node is in a `Condition` branch, like being in a separate room. The `End` node outside naturally cannot access its `result` variable.</p>
      </div>
    </div>
  </div>
  <div>
    <b>Variable Structure Insight: Easily Understand Complex Data</b>
    <p className="rs-tip">When a variable becomes complex (e.g., an object with many levels), the Variable Engine allows you to explore its internal structure layer by layer, like peeling an onion, with all details at your fingertips.</p>
    <img loading="lazy" src="/variable/variable-tree-management.gif" />
    <p style={{marginTop: '10px'}}>In this diagram, you can see the output variables of all nodes and their hierarchical relationships, like a lush tree.</p>
  </div>
  <div>
    <b>Automatic Type Inference: A Spark of Genius</b>
    <p className="rs-tip">You no longer need to tell each variable what its type should be. The Variable Engine, like your "soulmate," will automatically infer its type based on the context.</p>
    <img loading="lazy" src="/variable/variable-batch-auto-infer.gif" />
    <p style={{marginTop: '10px'}}>For example, when the type of the `arr` variable in the `Start` node changes, the type of the `item` output by the `Batch` node will also be automatically updated to ensure type consistency.</p>
  </div>
</div>

## How to Enable the Variable Engine?

You can enable the Variable Engine with a simple configuration to experience its powerful features.

[> View API Details](https://flowgram.ai/auto-docs/editor/interfaces/VariablePluginOptions.html)

```tsx pure title="use-editor-props.ts" {3}
// Enable the Variable Engine in EditorProps
{
  variableEngine: {
    // Set to true to enable the Variable Engine
    enable: true
  }
}
```
