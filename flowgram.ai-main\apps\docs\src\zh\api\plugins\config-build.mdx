# Build Config

## builderConfig

- Type: `RsbuildConfig`

Used to customize the configurations of Rsbuild. For detailed configurations, please refer to [Rsbuild - Config](https://rsbuild.dev/config/).

- Example: Use [resolve.alias](https://rsbuild.dev/config/resolve/alias) to configure path aliases:

```ts title="rspress.config.ts"
export default defineConfig({
  builderConfig: {
    resolve: {
      alias: {
        '@common': './src/common',
      },
    },
  },
});
```

- Example: Use [tools.rspack](https://rsbuild.dev/config/tools/rspack) to modify the Rspack configuration, such as registering a webpack or Rspack plugin:

```ts title="rspress.config.ts"
export default defineConfig({
  builderConfig: {
    tools: {
      rspack: async config => {
        const { default: ESLintPlugin } = await import('eslint-webpack-plugin');
        config.plugins?.push(new ESLintPlugin());
        return config;
      },
    },
  },
});
```

::: warning

If you want to modify the output directory, please use [outDir](/api/config/config-basic#outdir).

:::

## builderPlugins

- Type: `RsbuildPlugin[]`

Used to register [Rsbuild plugins](https://rsbuild.dev/plugins/list/).

You can use the rich plugins of Rsbuild in the Rspress project to quickly extend the building capabilities.

- Example: Support Vue SFC through [@rsbuild/plugin-vue](https://rsbuild.dev/plugins/list/plugin-vue)

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';
import { pluginVue } from '@rsbuild/plugin-vue';

export default defineConfig({
  builderPlugins: [pluginVue()],
});
```

- Example: Add Google analytics through [rsbuild-plugin-google-analytics](https://github.com/rspack-contrib/rsbuild-plugin-google-analytics)

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';
import { pluginGoogleAnalytics } from 'rsbuild-plugin-google-analytics';

export default defineConfig({
  builderPlugins: [
    pluginGoogleAnalytics({
      // replace this with your Google tag ID
      id: 'G-xxxxxxxxxx',
    }),
  ],
});
```

- Example: Add Open Graph meta tags through [rsbuild-plugin-open-graph](https://github.com/rspack-contrib/rsbuild-plugin-open-graph)

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';
import { pluginOpenGraph } from 'rsbuild-plugin-open-graph';

export default defineConfig({
  builderPlugins: [
    pluginOpenGraph({
      title: 'My Website',
      type: 'website',
      // ...options
    }),
  ],
});
```

You can also override the built-in plugins [@rsbuild/plugin-react](https://rsbuild.dev/plugins/list/plugin-react), [@rsbuild/plugin-sass](https://rsbuild.dev/plugins/list/plugin-sass) and [@rsbuild/plugin-less](https://rsbuild.dev/plugins/list/plugin-less), and customize relevant plugin options.

- Example: Modify related options of built-in [@rsbuild/plugin-less](https://rsbuild.dev/plugins/list/plugin-less) plugin

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';
import { pluginLess } from '@rsbuild/plugin-less';

export default defineConfig({
  builderPlugins: [
    pluginLess({
      lessLoaderOptions: {
        lessOptions: {
          math: 'always',
        },
      },
    }),
  ],
});
```

### Default Config

If you need to view the default Rspack or Rsbuild configs, you can add the `DEBUG=rsbuild` parameter when running the `rspress dev` or `rspress build` command:

```bash
DEBUG=rsbuild rspress dev
```

After execution, the `rsbuild.config.js` file is created in the `doc_build` directory, which contains the complete `builderConfig`.

> Please refer to [Rsbuild - Debug Mode](https://rsbuild.dev/guide/debug/debug-mode) for more information on how to debug the Rsbuild.

## markdown

- Type: `Object`

Configure MDX-related compilation abilities.

### markdown.remarkPlugins

- Type: `Array`
- Default: `[]`

Configure the remark plugins. for example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  markdown: {
    remarkPlugins: [
      [
        require('remark-autolink-headings'),
        {
          behavior: 'wrap',
        },
      ],
    ],
  },
});
```

### markdown.rehypePlugins

- Type: `Array`

Configure the rehype plugin. for example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  markdown: {
    rehypePlugins: [
      [
        require('rehype-autolink-headings'),
        {
          behavior: 'wrap',
        },
      ],
    ],
  },
});
```

### markdown.checkDeadLinks

- Type: `boolean`
- Default: `false`

Whether to check for dead links. for example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  markdown: {
    checkDeadLinks: true,
  },
});
```

After enabling this config, the framework will check the links in the document based on the conventional routing table. If there is an unreachable link, the build will throw an error and exit.

### markdown.mdxRs

- Type: `boolean | { include: (filepath: string) => boolean }`
- Default: `true`

### markdown.showLineNumbers

- Type: `boolean`

Whether to display the line number of the code block. Defaults to `false`.

### markdown.defaultWrapCode

- Type: `boolean`

Whether to enable long code line wrapping display by default. Defaults to `false`.

### markdown.globalComponents

- Type: `string[]`

Register component to the global scope, which will make it automatically available in every MDX file, without any import statements.For example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';
import path from 'path';

export default defineConfig({
  markdown: {
    globalComponents: [path.join(__dirname, 'src/src/components/Alert.tsx')],
  },
});
```

Then you can use the `Alert` component in any MDX file:

```mdx title="test.mdx"
<Alert type="info">This is a info alert</Alert>
```

:::danger Danger
Please set `markdown.mdxRs` to `false` when configuring `globalComponents`, otherwise the global components will not take effect.
:::

### markdown.highlightLanguages

- Type: `[string, string][]`
- Default:

```js
const DEFAULT_HIGHLIGHT_LANGUAGES = [
  ['js', 'javascript'],
  ['ts', 'typescript'],
  ['jsx', 'tsx'],
  ['xml', 'xml-doc'],
  ['md', 'markdown'],
  ['mdx', 'tsx'],
];
```

Rspress supports automatic import of highlighted languages and makes some language aliases by default.

- By default, it is implemented based on [Prism.js](https://prismjs.com/). You can also switch to Shiki through [@rspress/plugin-shiki](/plugin/official-plugins/shiki).
- The default configuration alias languages include `js`, `jsx`, `ts`, `tsx`, `xml`, `md`, `mdx`.

You can also extend these default aliases, such as:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';
import path from 'path';

export default defineConfig({
  markdown: {
    highlightLanguages: [
      // Alias as md, full name as markdown
      ['md', 'markdown'],
    ],
  },
});
```

The alias of each language is configured in the format of `[string, string]`. The former is the alias of the language, and the latter is the full name of the language. You can go to [File List](https://github.com/react-syntax-highlighter/react-syntax-highlighter/tree/master/src/languages/prism) to view the full names of all supported languages.
