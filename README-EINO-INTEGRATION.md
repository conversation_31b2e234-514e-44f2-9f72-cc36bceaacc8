# 🤖 Eino 集成指南

## 📋 概述

本项目现在支持两种运行模式：

1. **开发模式**：使用模拟数据，前端界面完全不变
2. **Eino 模式**：集成 Eino 后端，提供真实的 AI 能力

## 🚀 快速开始

### 方式1：一键启动（推荐）

**Linux/macOS:**
```bash
chmod +x start-with-eino.sh
./start-with-eino.sh
```

**Windows:**
```cmd
start-with-eino.bat
```

### 方式2：手动启动

1. **启动 Eino 后端**
```bash
cd eino-backend
go mod tidy
go run main.go
```

2. **启动前端（新终端）**
```bash
export REACT_APP_USE_EINO_BACKEND=true  # Linux/macOS
# 或
set REACT_APP_USE_EINO_BACKEND=true     # Windows

npm run dev
```

## 🔧 技术架构

### 前端（无需修改）
- React + TypeScript + Tailwind CSS
- 所有现有组件保持不变
- 通过环境变量切换数据源

### 后端（新增 Eino 集成）
- Go + Gin + Eino 框架
- 完全兼容前端 API 接口
- 使用 Eino Graph 处理 AI 逻辑

## 📊 功能对比

| 功能 | 开发模式 | Eino 模式 |
|------|----------|-----------|
| 应用管理 | ✅ 模拟数据 | ✅ 真实后端 |
| 应用创建 | ✅ 本地存储 | ✅ Eino Graph |
| 聊天测试 | ✅ 模拟回复 | ✅ AI 智能回复 |
| 界面体验 | ✅ 完全一致 | ✅ 完全一致 |

## 🎯 核心特性

### 1. 零界面改动
- 前端组件完全不变
- 用户体验保持一致
- 渐进式升级策略

### 2. 智能后端切换
```typescript
// 自动检测运行模式
if (isDevelopmentMode()) {
  // 使用模拟数据
} else {
  // 使用 Eino 后端
}
```

### 3. Eino Graph 编排
```go
// 每个 AI 应用对应一个 Eino Graph
chain := compose.NewChain[map[string]any, *schema.Message]().
    AppendChatTemplate(template).
    AppendChatModel(chatModel).
    Compile(ctx)
```

## 🔍 API 接口

### 应用管理
```
GET    /api/applications     # 获取应用列表
POST   /api/applications     # 创建应用
GET    /api/applications/:id # 获取应用详情
```

### 聊天功能
```
POST   /api/chat            # 发送消息
POST   /api/chat/stream     # 流式聊天（未来）
```

## 🛠 开发指南

### 添加新的 AI 组件

1. **在 Eino 后端添加组件**
```go
// 添加新的工具
toolsNode := compose.NewToolsNode(tools)
graph.AddToolsNode("tools", toolsNode)
```

2. **前端自动支持**
- 无需修改前端代码
- 通过 API 自动获取新功能

### 扩展聊天功能

1. **流式响应**
```go
// 后端支持流式输出
stream := graph.Stream(ctx, input)
```

2. **前端自动适配**
```typescript
// 使用流式 hook
const { messages, sendMessage } = useEinoStreamChat({ applicationId });
```

## 📝 配置说明

### 环境变量
```bash
# 启用 Eino 后端
REACT_APP_USE_EINO_BACKEND=true

# 后端地址（可选）
REACT_APP_EINO_BACKEND_URL=http://localhost:8080
```

### 模型配置
```go
// 在 eino-backend/main.go 中配置
chatModel := &openai.ChatModel{
    Model: "gpt-4",
    Temperature: 0.7,
}
```

## 🔧 故障排除

### 后端连接失败
1. 检查 Go 是否安装：`go version`
2. 检查端口是否被占用：`lsof -i :8080`
3. 查看后端日志：`logs/eino-backend.log`

### 前端显示模拟数据
1. 确认环境变量：`echo $REACT_APP_USE_EINO_BACKEND`
2. 检查后端是否启动：`curl http://localhost:8080/api/health`
3. 清除浏览器缓存

## 🚀 部署指南

### 开发环境
```bash
# 使用一键启动脚本
./start-with-eino.sh
```

### 生产环境
```bash
# 构建前端
npm run build

# 构建后端
cd eino-backend
go build -o eino-backend main.go

# 部署
./eino-backend &
serve -s dist -l 3000
```

## 📈 未来规划

### 短期目标
- [x] 基础 Eino 集成
- [x] 应用创建和管理
- [x] 聊天功能
- [ ] 流式响应
- [ ] 错误处理优化

### 中期目标
- [ ] 可视化 Graph 编辑器
- [ ] 更多 AI 组件支持
- [ ] 性能监控和分析
- [ ] 多模型支持

### 长期目标
- [ ] 企业级部署
- [ ] 插件生态系统
- [ ] AI DevOps 平台

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支：`git checkout -b feature/eino-enhancement`
3. 提交更改：`git commit -m 'Add Eino feature'`
4. 推送分支：`git push origin feature/eino-enhancement`
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

🎉 **现在你可以在不改变任何前端界面的情况下，享受 Eino 强大的 AI 能力！**
