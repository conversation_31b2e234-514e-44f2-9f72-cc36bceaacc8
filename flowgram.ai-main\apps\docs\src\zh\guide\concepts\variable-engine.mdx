# 变量引擎

## 整体设计

### 架构分层

:::warning 架构分层
变量引擎设计上遵循 DIP（依赖反转）原则，按照 代码稳定性、抽象层次 以及和 业务的远近 分为三层：
- 变量抽象层：变量架构中抽象层次最高，代码也最为稳定的部分
- 变量实现层：变量架构中变动较大，不同业务之间通常存在调整的部分
- 变量业务层：变量架构中提供给业务的 Facade ，与画布引擎、节点引擎联动的部分

:::

![架构分层图](@/public/variable-engine.png)


### 术语表

#### 🌟 作用域（Scope）
:::warning ⭐️⭐️⭐️ 定义：
一种约定的空间，空间内 通过 AST 来描述变量声明和消费情况
- 约定的空间：空间是什么，完全由业务定义
- 在低代码设计态中，可以是一个节点、一个组件、一个右侧面板...
- 在一段代码中，可以是一行 Statement、一段代码块、一个函数、一个文件...

:::

作用域的空间是什么？可以由不同的业务来划定。


#### 🌟 抽象语法树（AST）

:::warning 定义：
⭐️⭐️⭐️ 一种协议，通过树的形式，组合 AST 节点，实现对变量信息的显式/隐式 CRUD
- AST 节点：AST 中可响应式的协议节点
- 显式 CRUD，如：业务显示设定一个变量的变量类型
- 隐式 CRUD，如：业务声明一个变量，变量会根据其初始化参数自动推导变量类型

:::

:::warning 作用域里面的变量、类型、表达式、结构体 等等变量信息... 本质上都是 AST 节点的组合
- 变量 -> VariableDeclaration 节点
- 表达式 -> Expression 节点
- 类型 -> TypeNode 节点
- 结构体 -> StructDeclaration 节点

:::

参考链接：https://ts-ast-viewer.com/

#### 变量（Variable）

:::warning 定义：
一种用于声明新变量的 AST 节点，通过唯一标识符 指向一个 在特定集合范围内变动的值
- 在特定集合范围内变动的值：变量的值必须在 变量类型 描述的范围内
- 唯一标识符：变量必须有一个唯一的 Key 值

:::

[JavaScript中的变量，唯一 Key + 指向一个变动的值](@/public/variable-code.png)

#### 变量类型（Variable Type）

:::warning 定义：
⭐️⭐️⭐️ 一种 AST 节点，用于约束一个变量，被约束的变量值只能在预先设定的集合范围内变动
- 一个变量可以绑定一个变量类型

:::
<table>
  <tr>
    <td><img loading="lazy" src="/variable-type1.png"/></td>
    <td><img loading="lazy" src="/variable-type2.png"/></td>
  </tr>
</table>

### 变量引擎的形象理解

:::warning 想像这样一个变量引擎的世界：
- 通过一个个 作用域 来划定出一个个 国家
- 每个国家包含三大公民：声明、类型、表达式
- 国家与国家之间通过 作用域链 来实现交流

:::

![图解](@/public/varaible-zone.png)
