# Introduction

FlowGram is a node-based flow building engine that helps developers quickly create workflows in either fixed layout or free connection layout modes, providing a set of interactive best practices. It's particularly suitable for visual workflows with clear inputs and outputs.

In today's AI-driven era, we are focusing more on how to empower workflows with AI, hence the AI suffix in our name.

<div className="rs-highlight">
  FlowGram = Flow + Program, suggesting that flows are like programs, with
  Condition, Loop, and even TryCatch nodes.
</div>

## Official Demo

<div style={{ marginTop: 16, display: "flex", gap: 8 }}>
  <div>
    <div>
      <a
        className="rs-link"
        href="/en/examples/fixed-layout/fixed-feature-overview.html"
      >
        Fixed Layout
      </a>
    </div>
    <div className="rs-tip" style={{ height: 84 }}>
      Fixed layout with nodes/branches supporting specified position drag and
      drop, offering compound nodes like branches and loops
    </div>
    <div>
      <img loading="lazy" src="/fixed-layout/fixed-layout-demo.gif" />
    </div>
  </div>
  <div>
    <div>
      <a
        className="rs-link"
        href="/en/examples/free-layout/free-feature-overview.html"
      >
        Free Connection Layout
      </a>
    </div>
    <div className="rs-tip" style={{ height: 84 }}>
      Free layout where nodes can be moved to any position and connected through
      free connections
    </div>
    <div>
      <img loading="lazy" src="/free-layout/free-layout-demo.gif" />
    </div>
  </div>
</div>

## Interactive Experience

Providing a set of interactive best practices for smoother workflow operations

<table className="rs-table">
  <tr>
    <td>Motion Transitions</td>
    <td>
      <p>
        Motion animations in web applications can be traced back to Material
        Design, which suggests that element changes in width, height, or
        position need a transition process. The canvas engine separates the
        drawing of lines and nodes, greatly reducing the cost of implementing
        motion transitions
      </p>
      <div className="rs-center">
        <img loading="lazy" src="/common/motion.gif" />
      </div>
    </td>
  </tr>
  <tr>
    <td>Touchpad Gesture Zoom + Space Key Canvas Drag</td>
    <td>
      <p>
        Gestures refer to Mac touchpad two-finger spread/pinch for canvas zoom
        in/out, or holding space to drag the canvas, interactions inspired by
        Sketch and Figma
      </p>
      <div className="rs-center">
        <img loading="lazy" src="/common/touch-pad.gif" />
      </div>
    </td>
  </tr>
  <tr>
    <td>Minimap</td>
    <td>
      <div className="rs-center">
        <img loading="lazy" src="/fixed-layout/minimap.gif" />
      </div>
    </td>
  </tr>
  <tr>
    <td>Undo/Redo</td>
    <td>
      <div className="rs-center">
        <img loading="lazy" src="/fixed-layout/redo-undo.gif" />
      </div>
    </td>
  </tr>
  <tr>
    <td>Copy/Paste (Shortcut Support)</td>
    <td>
      <div className="rs-center">
        <img loading="lazy" src="/fixed-layout/copypaste.gif" />
      </div>
    </td>
  </tr>
  <tr>
    <td>
      <div>
        <div>Box Selection + Drag and Drop</div>
        <div>(Fixed)</div>
      </div>
    </td>
    <td>
      <div className="rs-center">
        <div className="rs-center">
          <img loading="lazy" src="/fixed-layout/dragdrop.gif" />
        </div>
      </div>
    </td>
  </tr>
  <tr>
    <td>
      <div>Horizontal/Vertical Layout Switch</div>
      <div>(Fixed)</div>
    </td>
    <td>
      <div className="rs-center">
        <img loading="lazy" src="/fixed-layout/layout-change.gif" />
      </div>
    </td>
  </tr>
  <tr>
    <td>
      <div>Branch Folding</div>
      <div>(Fixed)</div>
    </td>
    <td>
      <div className="rs-center">
        <img loading="lazy" src="/fixed-layout/fold.gif" />
      </div>
    </td>
  </tr>
  <tr>
    <td>
      <div>Grouping</div>
      <div>(Fixed)</div>
    </td>
    <td>
      <div className="rs-center">
        <img loading="lazy" src="/fixed-layout/group.gif" />
      </div>
    </td>
  </tr>
  <tr>
    <td>
      Auto Layout
      <div>(Free)</div>
    </td>
    <td>
      <div className="rs-center">
        <img loading="lazy" src="/free-layout/autolayout.gif" />
      </div>
    </td>
  </tr>
  <tr>
    <td>
      Snap Alignment + Guidelines
      <div>(Free)</div>
    </td>
    <td>
      <div className="rs-center">
        <img loading="lazy" src="/free-layout/snap.gif" />
      </div>
    </td>
  </tr>
  <tr>
    <td>
      Coze Loop Sub-canvas
      <div>(Free)</div>
    </td>
    <td>
      <div className="rs-center">
        <img loading="lazy" src="/free-layout/loop.gif" />
      </div>
    </td>
  </tr>
</table>

## Online Applications

<div style={{ marginTop: 16, display: "flex", gap: 8 }}>
  <div>
    <div>
      <a
        className="rs-link"
        href="https://www.coze.com/open/docs/guides/workflow"
        target="_blank"
      >
        Coze Workflow
      </a>
    </div>
    <div>
      <img loading="lazy" src="/ref-coze-en.png" />
    </div>
  </div>
  <div>
    <a
      className="rs-link"
      href="https://ae.feishu.cn/hc/zh-CN/articles/120610822514"
      target="_blank"
    >
      Lark Low-code Platform Workflow
    </a>
    <div>
      <img loading="lazy" src="/ref-apaas-en.png" />
    </div>
  </div>
  <div>
    <a
      className="rs-link"
      href="https://www.feishu.cn/hc/en-US/articles/************-overview-of-workflow-in-base"
      target="_blank"
    >
      Lark Base Workflow
    </a>
    <div>
      <img loading="lazy" src="/ref-bitable-en.png" />
    </div>
  </div>
</div>
