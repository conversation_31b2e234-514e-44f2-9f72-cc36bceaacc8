name: E2E Tests

on:
  pull_request:
    branches: [ "main" ]
  merge_group:
    branches: [ "main" ]

jobs:
  e2e:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Rush Install
        run: node common/scripts/install-run-rush.js install

      - name: Rush build
        run: node common/scripts/install-run-rush.js build

      - name: Install Playwright Browsers
        run: pushd e2e/fixed-layout && npx playwright install --with-deps && popd

      - name: Run E2E tests
        run: node common/scripts/install-run-rush.js e2e:test --verbose
