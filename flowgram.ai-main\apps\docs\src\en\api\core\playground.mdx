# Playground

Canvas instance

[> API Detail](https://flowgram.ai/auto-docs/core/classes/Playground.html)

```ts pure
const ctx = useClientContext()

console.log(ctx.playground)

```
## config

Canvas configuration, provides zoom, scroll, etc.

[> API Detail](https://flowgram.ai/auto-docs/core/classes/PlaygroundConfigEntity.html)

### Properties
- zoom `number` Current zoom ratio
- scrollData `{ scrollX: number, scrollY: number }` Current scroll position
- readonlyOrDisabled `boolean` Whether the canvas is readonly or disabled
- readonly
- disabled


### fitView

Node fit canvas window, need to pass in the node's bounds

```ts pure
/**
 * Fit size
 * @param bounds {Rectangle} Target size
 * @param easing {number} Whether to start animation, default is true
 * @param padding {number} Boundary padding
 */
ctx.playground.config.fitView(ctx.document.root.bounds, true, 10)
```

### scrollToView

Specify the node position and scroll to the canvas visible area, if the position is already in the visible area, it will not scroll unless `scrollTo<PERSON><PERSON>` is forced to scroll

```ts pure

/**
 * Detailed parameter description
 * @param opts {PlaygroundConfigRevealOpts}
**/
interface PlaygroundConfigRevealOpts {
  entities?: Entity[]
  position?: PositionSchema // Scroll to the specified position and center
  bounds?: Rectangle // Scroll bounds
  scrollDelta?: PositionSchema
  zoom?: number // Need to scale the ratio
  easing?: boolean // Whether to start animation, default is true
  easingDuration?: number // Default 500 ms
  scrollToCenter?: boolean // Whether to force scroll to center
}

ctx.playground.config.scrollToView({
  bounds: ctx.document.getNode('start').bounds,
})
```

### zoomin

Zoom In

### zoomout

Zoom Out

### getPoseFromMouseEvent

Convert browser mouse position to canvas coordinate system

```ts pure

const pos: { x: number, y: number } = ctx.playground.config.getPoseFromMouseEvent(domMouseEvent)

```

### scroll

Scroll canvas, need to pass in the scroll position, and whether to smooth scroll, scroll time

```ts pure
ctx.playground.config.scroll({ scrollX: 100, scrollY: 100 }, true, 300)
```

### getViewport

Get the current canvas viewport size

```ts pure
const viewport = ctx.playground.config.getViewport()
```
