/**
 * Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

'use client';

// import { FlowNodeRegistry } from '../typings';
// import { ConditionNodeRegistry } from './condition';
// import { CommentNodeRegistry } from './comment';
// import { LoopNodeRegistry } from './loop';
// import { LLMNodeRegistry } from './llm';
// import { EndNodeRegistry } from './end';
// import { WorkflowNodeType } from './constants';
// import { StartNodeRegistry } from './start';

export { WorkflowNodeType } from './constants';

// export const nodeRegistries: FlowNodeRegistry[] = [
//   // ConditionNodeRegistry,
//   StartNodeRegistry,
//   // EndNodeRegistry,
//   // LLMNodeRegistry,
//   // LoopNodeRegistry,
//   // CommentNodeRegistry,
// ];

// export const visibleNodeRegistries = nodeRegistries.filter(
//   (r) => r.type !== WorkflowNodeType.Comment
// );
