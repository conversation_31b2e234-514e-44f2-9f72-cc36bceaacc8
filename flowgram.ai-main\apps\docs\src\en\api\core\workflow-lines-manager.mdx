# WorkflowLinesManager (free)

Free layout line management, currently attached to the free layout document

[> API Detail](https://flowgram.ai/auto-docs/free-layout-core/classes/WorkflowLinesManager.html)

```
import { useClientContext } from '@flowgram.ai/free-layout-editor'

const ctx = useClientContext();
console.log(ctx.document.linesManager)
```

## getAllLines

Get all line entities

```ts pure
const allLines = ctx.document.linesManager.getAllLines()

```

## toJSON

Export line data

```ts pure
const json = ctx.document.linesManager.toJSON()
```

## Custom Arrow Renderer

WorkflowLinesManager supports customizing arrow styles through the renderer registry. For detailed usage, please refer to the [Line Configuration Guide](/en/guide/advanced/free-layout/line#4-custom-arrow-renderer) documentation.

```tsx
// Simple example: Register custom arrow
const editorProps = {
  materials: {
    components: {
      'arrow-renderer': MyCustomArrow,
    },
  },
};
```
