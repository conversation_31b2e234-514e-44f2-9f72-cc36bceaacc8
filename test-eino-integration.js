// 测试 Eino 集成的简单脚本
import express from 'express';
import cors from 'cors';

const app = express();
const PORT = 8082;

app.use(cors());
app.use(express.json());

// 模拟应用存储
const applications = new Map();

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    backend: 'Eino Integration Test',
    timestamp: new Date().toISOString(),
    message: '🎉 Eino 集成测试成功！'
  });
});

// 获取应用列表
app.get('/api/applications', (req, res) => {
  const apps = Array.from(applications.values());
  console.log(`📱 返回 ${apps.length} 个应用`);
  res.json({ applications: apps });
});

// 创建应用
app.post('/api/applications', (req, res) => {
  const { name, description, avatar } = req.body;
  
  const appId = `test_app_${Date.now()}`;
  const newApp = {
    id: appId,
    name: name || '测试应用',
    description: description || '这是一个测试应用',
    avatar: avatar || '🤖',
    isPublished: false,
    lastEdited: new Date().toISOString()
  };
  
  applications.set(appId, newApp);
  
  console.log(`✅ 创建应用成功: ${newApp.name} (ID: ${appId})`);
  res.json({ application: newApp });
});

// 聊天接口
app.post('/api/chat', async (req, res) => {
  const { applicationId, message } = req.body;
  
  console.log(`💬 收到聊天消息: ${message}`);
  
  // 模拟处理时间
  await new Promise(resolve => setTimeout(resolve, 500));
  
  const response = {
    id: Date.now(),
    type: 'assistant',
    content: `🤖 Eino 集成测试回复：您说了"${message}"。这证明前端和后端集成成功！当前时间：${new Date().toLocaleTimeString()}`
  };
  
  console.log(`🚀 发送回复: ${response.content.substring(0, 50)}...`);
  res.json({ message: response });
});

app.listen(PORT, () => {
  console.log('');
  console.log('🎯 ===== Eino 集成测试服务器 =====');
  console.log(`🚀 服务地址: http://localhost:${PORT}`);
  console.log(`🔧 健康检查: http://localhost:${PORT}/api/health`);
  console.log('');
  console.log('📋 测试步骤:');
  console.log('1. 在浏览器中访问前端: http://localhost:5173');
  console.log('2. 设置环境变量: REACT_APP_USE_EINO_BACKEND=true');
  console.log('3. 修改 API_BASE_URL 为: http://localhost:8082/api');
  console.log('4. 测试创建应用和聊天功能');
  console.log('');
  console.log('✨ 如果看到这个消息，说明 Node.js 版本的 Eino 后端启动成功！');
});
