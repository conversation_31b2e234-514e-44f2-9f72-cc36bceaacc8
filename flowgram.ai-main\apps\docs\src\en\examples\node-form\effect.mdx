---
outline: false
---


# Effect

import { NodeFormEffectPreview } from '../../../../components';

The following examples demonstrate how to configure form side effects. Two examples are provided, with behaviors described below:
1. Basic effect: When a form field value changes, the current form values will be printed to the console.
2. Control other fields: When the current form field data changes, it will simultaneously change the value of another form field.


<NodeFormEffectPreview />
