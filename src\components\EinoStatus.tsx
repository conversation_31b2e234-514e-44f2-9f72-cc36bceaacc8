import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Wifi, WifiOff, Server, AlertCircle } from 'lucide-react';
import { isDevelopmentMode } from '../services/api';

interface EinoStatusProps {
  className?: string;
}

export const EinoStatus: React.FC<EinoStatusProps> = ({ className = '' }) => {
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [backendInfo, setBackendInfo] = useState<any>(null);
  const [isChecking, setIsChecking] = useState(false);

  const checkConnection = async () => {
    if (isDevelopmentMode()) {
      setIsConnected(true);
      setBackendInfo({ backend: 'Development Mode', message: '使用模拟数据' });
      return;
    }

    setIsChecking(true);
    try {
      const response = await fetch('http://localhost:8082/api/health');
      if (response.ok) {
        const data = await response.json();
        setIsConnected(true);
        setBackendInfo(data);
      } else {
        setIsConnected(false);
        setBackendInfo(null);
      }
    } catch (error) {
      setIsConnected(false);
      setBackendInfo(null);
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    checkConnection();
    // 每30秒检查一次连接状态
    const interval = setInterval(checkConnection, 30000);
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = () => {
    if (isChecking) return 'text-yellow-500';
    if (isConnected === null) return 'text-gray-400';
    return isConnected ? 'text-green-500' : 'text-red-500';
  };

  const getStatusIcon = () => {
    if (isChecking) return <Server className="animate-pulse" size={16} />;
    if (isConnected === null) return <AlertCircle size={16} />;
    return isConnected ? <Wifi size={16} /> : <WifiOff size={16} />;
  };

  const getStatusText = () => {
    if (isChecking) return '检查中...';
    if (isConnected === null) return '未知';
    if (isConnected) {
      return isDevelopmentMode() ? '开发模式' : 'Eino 已连接';
    }
    return 'Eino 未连接';
  };

  return (
    <motion.div
      className={`flex items-center space-x-2 px-3 py-1.5 rounded-lg bg-white/80 backdrop-blur-sm border border-slate-200/60 shadow-sm ${className}`}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      whileHover={{ scale: 1.02 }}
    >
      <motion.div
        className={getStatusColor()}
        animate={isChecking ? { rotate: 360 } : {}}
        transition={isChecking ? { duration: 2, repeat: Infinity, ease: "linear" } : {}}
      >
        {getStatusIcon()}
      </motion.div>
      
      <span className={`text-sm font-medium ${getStatusColor()}`}>
        {getStatusText()}
      </span>
      
      {backendInfo && (
        <motion.div
          className="text-xs text-slate-500 ml-2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          {backendInfo.backend}
        </motion.div>
      )}
      
      {!isDevelopmentMode() && (
        <motion.button
          onClick={checkConnection}
          className="text-xs text-slate-400 hover:text-slate-600 transition-colors"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          disabled={isChecking}
        >
          刷新
        </motion.button>
      )}
    </motion.div>
  );
};

// 简化版状态指示器
export const EinoStatusBadge: React.FC<{ className?: string }> = ({ className = '' }) => {
  const [isConnected, setIsConnected] = useState<boolean | null>(null);

  useEffect(() => {
    const checkConnection = async () => {
      if (isDevelopmentMode()) {
        setIsConnected(true);
        return;
      }

      try {
        const response = await fetch('http://localhost:8082/api/health');
        setIsConnected(response.ok);
      } catch {
        setIsConnected(false);
      }
    };

    checkConnection();
  }, []);

  if (isConnected === null) return null;

  return (
    <motion.div
      className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${
        isConnected 
          ? 'bg-green-100 text-green-700' 
          : 'bg-red-100 text-red-700'
      } ${className}`}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
    >
      <div className={`w-2 h-2 rounded-full ${
        isConnected ? 'bg-green-500' : 'bg-red-500'
      }`} />
      <span>{isConnected ? 'Eino' : '离线'}</span>
    </motion.div>
  );
};
