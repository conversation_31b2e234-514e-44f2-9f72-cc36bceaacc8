# Installation

import { PackageManagerTabs } from '@theme';

## Install via npx

<PackageManagerTabs command={{
  npm: "npx @flowgram.ai/create-app@latest"
}} />

```shell
# Select demo
- fixed-layout # Best practices for fixed layout
- free-layout # Best practices for free layout
- fixed-layout-simple # Basic usage of fixed layout
- free-layout-simple # Basic usage of free layout

```

## Install via npm

<PackageManagerTabs command={{
  npm: "npm install @flowgram.ai/fixed-layout-editor"
}} />

<PackageManagerTabs command={{
  npm: "npm install @flowgram.ai/free-layout-editor"
}} />

