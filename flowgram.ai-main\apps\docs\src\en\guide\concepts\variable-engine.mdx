# Variable Engine

## Overall Design

### Architecture Layers

:::warning Architecture Layers
The variable engine is designed to follow the DIP (Dependency Inversion Principle). It is divided into three layers according to code stability, abstraction level, and proximity to the business:
- Variable Abstraction Layer: The part with the highest level of abstraction and the most stable code in the variable architecture.
- Variable Implementation Layer: The part that changes significantly and usually requires adjustments between different businesses in the variable architecture.
- Variable Business Layer: The Facade provided to the business in the variable architecture, which interacts with the canvas engine and node engine.

:::

![Architecture Layers Diagram](@/public/en-variable-engine.png)

### Glossary

#### 🌟 Scope
:::warning ⭐️⭐️⭐️ Definition:
A predefined space where the declaration and consumption of variables are described through an AST.
- Predefined space: The nature of the space is entirely defined by the business.
- In the low-code design state, it can be a node, a component, a right-side panel, etc.
- In a piece of code, it can be a single statement, a code block, a function, a file, etc.

:::

What the scope space is can be defined by different businesses.

#### 🌟 Abstract Syntax Tree (AST)

:::warning Definition:
⭐️⭐️⭐️ A protocol that combines AST nodes in a tree structure to explicitly or implicitly perform CRUD operations on variable information.
- AST nodes: Reactive protocol nodes in the AST.
- Explicit CRUD, e.g., the business explicitly sets the variable type of a variable.
- Implicit CRUD, e.g., the business declares a variable, and the variable type is automatically inferred based on its initialization parameters.

:::

:::warning Variable information such as variables, types, expressions, and structures within the scope... are essentially combinations of AST nodes.
- Variable -> VariableDeclaration node
- Expression -> Expression node
- Type -> TypeNode node
- Structure -> StructDeclaration node

:::

Reference link: https://ts-ast-viewer.com/

#### Variable

:::warning Definition:
An AST node used to declare a new variable, which points to a value that changes within a specific set of ranges through a unique identifier.
- A value that changes within a specific set of ranges: The value of the variable must be within the range described by the variable type.
- Unique identifier: The variable must have a unique key value.

:::

[Variables in JavaScript, with a unique key + pointing to a changing value](@/public/variable-code.png)

#### Variable Type

:::warning Definition:
⭐️⭐️⭐️ An AST node used to constrain a variable. The value of the constrained variable can only change within a pre - set set of ranges.
- A variable can be bound to a variable type.

:::
<table>
  <tr>
    <td><img loading="lazy" src="/en-variable-type1.png"/></td>
    <td><img loading="lazy" src="/variable-type2.png"/></td>
  </tr>
</table>

### An Intuitive Understanding of the Variable Engine

:::warning Imagine a world of the variable engine:
- Different scopes are used to define different "countries".
- Each "country" contains three main "citizens": declarations, types, and expressions.
- Communication between "countries" is achieved through the scope chain.

:::

![Illustration](@/public/en-varaible-zone.png)
