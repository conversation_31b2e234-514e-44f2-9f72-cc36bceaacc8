# FlowGram.AI

## 指引

- [介绍](/guide/introduction.md)

## 例子

- [预览](/examples/index.md)
- [基础用法](/examples/fixed-layout/fixed-layout-simple.md)
- [复合节点](/examples/fixed-layout/fixed-composite-nodes.md)
- [最佳实践](/examples/fixed-layout/fixed-feature-overview.md)
- [基础用法](/examples/free-layout/free-layout-simple.md)
- [最佳实践](/examples/free-layout/free-feature-overview.md)
- [基础用法](/examples/node-form/basic.md)
- [副作用](/examples/node-form/effect.md)
- [数组](/examples/node-form/array.md)
- [联动](/examples/node-form/dynamic.md)

## 常用 API

- [常用 API](/api/common-apis.md)
- [API 预览](/api/index.md)
- [](/api/plugins.md)
- [Basic Config](/api/plugins/config-basic.md)
- [Build Config](/api/plugins/config-build.md)
- [Front Matter Config](/api/plugins/config-frontmatter.md)
- [Theme Config](/api/plugins/config-theme.md)
- [FlowDocument](/api/core/flow-document.md)
- [FlowNodeEntity/WorkflowNodeEntity](/api/core/flow-node-entity.md)
- [WorkflowDocument (free)](/api/core/workflow-document.md)
- [WorkflowLinesManager (free)](/api/core/workflow-lines-manager.md)
- [WorkflowLineEntity (free)](/api/core/workflow-line-entity.md)
- [Playground](/api/core/playground.md)
- [useClientContext](/api/hooks/use-client-context.md)
- [useNodeRender](/api/hooks/use-node-render.md)
- [usePlaygroundTools](/api/hooks/use-playground-tools.md)
- [useRefresh](/api/hooks/use-refresh.md)
- [useService](/api/hooks/use-service.md)
- [EditorRenderer](/api/components/editor-renderer.md)
- [FixedLayoutEditorProvider](/api/components/fixed-layout-editor-provider.md)
- [FixedLayoutEditor](/api/components/fixed-layout-editor.md)
- [FreeLayoutEditorProvider](/api/components/free-layout-editor-provider.md)
- [FreeLayoutEditor](/api/components/free-layout-editor.md)
- [WorkflowNodeRenderer(free)](/api/components/workflow-node-renderer.md)
- [ClipboardService](/api/services/clipboard-service.md)
- [CommandService](/api/services/command-service.md)
- [FlowOperationService](/api/services/flow-operation-service.md)
- [](/api/services/history-service.md)
- [SelectionService](/api/services/selection-service.md)
- [DisposableCollection](/api/utils/disposable-collection.md)
- [Disposable](/api/utils/disposable.md)
- [Emitter](/api/utils/emitter.md)
- [getNodeForm](/api/utils/get-node-form.md)

## Other

- [自定义插件](/guide/advanced/custom-plugin.md)
- [自定义 Service](/guide/advanced/custom-service.md)
- [复合节点](/guide/advanced/fixed-layout/composite-nodes.md)
- [加载与保存](/guide/advanced/fixed-layout/load.md)
- [节点](/guide/advanced/fixed-layout/node.md)
- [官方表单物料](/guide/advanced/form-materials.md)
- [节点表单](/guide/advanced/form.md)
- [线条](/guide/advanced/free-layout/line.md)
- [加载与保存](/guide/advanced/free-layout/load.md)
- [节点](/guide/advanced/free-layout/node.md)
- [端口](/guide/advanced/free-layout/port.md)
- [子画布](/guide/advanced/free-layout/sub-canvas.md)
- [历史记录](/guide/advanced/history.md)
- [缩略图](/guide/advanced/minimap.md)
- [快捷键](/guide/advanced/shortcuts.md)
- [变量基础](/guide/advanced/variable/basic.md)
- [消费变量](/guide/advanced/variable/variable-consume.md)
- [输出变量](/guide/advanced/variable/variable-output.md)
- [不使用表单](/guide/advanced/without-form.md)
- [画布引擎](/guide/concepts/canvas-engine.md)
- [ECS](/guide/concepts/ecs.md)
- [概念](/guide/concepts/index.md)
- [IOC](/guide/concepts/ioc.md)
- [节点引擎](/guide/concepts/node-engine.md)
- [对比 ReactFlow](/guide/concepts/reactflow.md)
- [变量引擎](/guide/concepts/variable-engine.md)
- [联系我们](/guide/contact-us.md)
- [创建固定布局画布](/guide/getting-started/create-fixed-layout-simple.md)
- [创建自由布局画布](/guide/getting-started/create-free-layout-simple.md)
- [安装](/guide/getting-started/install.md)
- [常见问题](/guide/question.md)