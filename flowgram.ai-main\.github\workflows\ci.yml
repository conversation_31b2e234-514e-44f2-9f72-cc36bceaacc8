name: CI
on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]
  merge_group:
    branches: [ "main" ]
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - name: Config Git User
        run: |
          git config --local user.name "dragooncjw"
          git config --local user.email "<EMAIL>"
      - name: For Debug
        run: |
          echo "Listing files in the root directory:"
          ls -alh
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      # - name: Verify Change Logs
      #   run: node common/scripts/install-run-rush.js change --verify
      - name: <PERSON> Install
        run: node common/scripts/install-run-rush.js install
      - name: Rush build
        run: node common/scripts/install-run-rush.js build
      - name: Check Lint
        run: node common/scripts/install-run-rush.js lint --verbose
      - name: Check TS
        run: node common/scripts/install-run-rush.js ts-check
      - name: Test (coverage)
        run: node common/scripts/install-run-rush.js test:cov
