import { useState, useCallback } from 'react';
import { apiService, ChatMessage, isDevelopmentMode } from '../services/api';

interface UseChatOptions {
  applicationId: string;
}

interface UseChatReturn {
  messages: ChatMessage[];
  isLoading: boolean;
  error: string | null;
  sendMessage: (content: string) => Promise<void>;
  clearMessages: () => void;
}

export const useEinoChat = ({ applicationId }: UseChatOptions): UseChatReturn => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now(),
      type: 'user',
      content: content.trim(),
    };

    // 立即添加用户消息
    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);
    setError(null);

    try {
      if (isDevelopmentMode()) {
        // 开发模式：模拟响应
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const assistantMessage: ChatMessage = {
          id: Date.now() + 1,
          type: 'assistant',
          content: `这是一个模拟响应。您说："${content}"。在实际环境中，这里会显示 Eino 后端的智能回复。`,
        };
        
        setMessages(prev => [...prev, assistantMessage]);
      } else {
        // 生产模式：使用 Eino 后端
        const response = await apiService.chat({
          applicationId,
          message: content,
          history: messages,
        });

        setMessages(prev => [...prev, response]);
      }
    } catch (err) {
      console.error('Chat error:', err);
      setError(err instanceof Error ? err.message : 'Failed to send message');
      
      // 添加错误消息
      const errorMessage: ChatMessage = {
        id: Date.now() + 1,
        type: 'assistant',
        content: '抱歉，我现在无法回复。请稍后再试。',
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  }, [applicationId, messages, isLoading]);

  const clearMessages = useCallback(() => {
    setMessages([]);
    setError(null);
  }, []);

  return {
    messages,
    isLoading,
    error,
    sendMessage,
    clearMessages,
  };
};

// 流式聊天 hook（未来扩展）
export const useEinoStreamChat = ({ applicationId }: UseChatOptions) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const sendStreamMessage = useCallback(async (content: string) => {
    if (!content.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now(),
      type: 'user',
      content: content.trim(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);
    setError(null);

    try {
      if (isDevelopmentMode()) {
        // 开发模式：模拟流式响应
        const assistantMessage: ChatMessage = {
          id: Date.now() + 1,
          type: 'assistant',
          content: '',
        };

        setMessages(prev => [...prev, assistantMessage]);

        const fullResponse = `这是一个模拟的流式响应。您说："${content}"。`;
        
        // 模拟逐字显示
        for (let i = 0; i <= fullResponse.length; i++) {
          await new Promise(resolve => setTimeout(resolve, 50));
          setMessages(prev => 
            prev.map(msg => 
              msg.id === assistantMessage.id 
                ? { ...msg, content: fullResponse.slice(0, i) }
                : msg
            )
          );
        }
      } else {
        // 生产模式：使用 Eino 流式接口
        const stream = await apiService.streamChat({
          applicationId,
          message: content,
          history: messages,
        });

        const assistantMessage: ChatMessage = {
          id: Date.now() + 1,
          type: 'assistant',
          content: '',
        };

        setMessages(prev => [...prev, assistantMessage]);

        const reader = stream.getReader();
        
        try {
          while (true) {
            const { done, value } = await reader.read();
            
            if (done) break;
            
            // 更新消息内容
            setMessages(prev => 
              prev.map(msg => 
                msg.id === assistantMessage.id 
                  ? { ...msg, content: msg.content + value.content }
                  : msg
              )
            );
          }
        } finally {
          reader.releaseLock();
        }
      }
    } catch (err) {
      console.error('Stream chat error:', err);
      setError(err instanceof Error ? err.message : 'Failed to send message');
    } finally {
      setIsLoading(false);
    }
  }, [applicationId, messages, isLoading]);

  const clearMessages = useCallback(() => {
    setMessages([]);
    setError(null);
  }, []);

  return {
    messages,
    isLoading,
    error,
    sendMessage: sendStreamMessage,
    clearMessages,
  };
};

// 连接状态 hook
export const useEinoConnection = () => {
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [isChecking, setIsChecking] = useState(false);

  const checkConnection = useCallback(async () => {
    if (isDevelopmentMode()) {
      setIsConnected(true);
      return true;
    }

    setIsChecking(true);
    try {
      const connected = await fetch('http://localhost:8080/api/health')
        .then(res => res.ok)
        .catch(() => false);
      
      setIsConnected(connected);
      return connected;
    } catch {
      setIsConnected(false);
      return false;
    } finally {
      setIsChecking(false);
    }
  }, []);

  return {
    isConnected,
    isChecking,
    checkConnection,
  };
};
