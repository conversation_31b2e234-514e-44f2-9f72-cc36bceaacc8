import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Search, Filter, Plus, Sparkles } from 'lucide-react';
import { AIApplication } from '../types';
import { AIApplicationCard } from './AIApplicationCard';
import { CreateApplicationModal } from './CreateApplicationModal';
import { EinoStatus } from './EinoStatus';

interface AIApplicationsViewProps {
  applications: AIApplication[];
  onCreateApplication: (data: { name: string; description: string; avatar: string }) => string;
  onEditApplication: (applicationId: string) => void;
}

export const AIApplicationsView: React.FC<AIApplicationsViewProps> = ({
  applications,
  onCreateApplication,
  onEditApplication,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [publishFilter, setPublishFilter] = useState<'all' | 'published' | 'unpublished'>('all');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  const filteredApplications = useMemo(() => {
    return applications.filter((app) => {
      const matchesSearch = app.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           app.description.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesFilter = publishFilter === 'all' || 
                          (publishFilter === 'published' && app.isPublished) ||
                          (publishFilter === 'unpublished' && !app.isPublished);
      
      return matchesSearch && matchesFilter;
    }).sort((a, b) => b.lastEdited.getTime() - a.lastEdited.getTime());
  }, [applications, searchTerm, publishFilter]);

  const handleCreateApplication = (data: { name: string; description: string; avatar: string }) => {
    const newApplicationId = onCreateApplication(data);
    // Automatically navigate to edit the new application
    setTimeout(() => {
      onEditApplication(newApplicationId);
    }, 100);
    return newApplicationId;
  };

  return (
    <div className="flex-1 p-8 bg-gradient-to-br from-slate-50 via-white to-blue-50/30 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div 
          className="flex items-center justify-between mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center space-x-4">
            <div>
              <motion.h1
                className="text-3xl font-bold bg-gradient-to-r from-slate-900 via-blue-800 to-purple-800 bg-clip-text text-transparent"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2, duration: 0.6 }}
              >
                AI 应用
              </motion.h1>
              <motion.p
                className="text-slate-600 mt-2 text-lg"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3, duration: 0.6 }}
              >
                管理你的智能应用生态
              </motion.p>
            </div>

            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.5, duration: 0.4 }}
            >
              <EinoStatus />
            </motion.div>
          </div>
          
          <motion.button
            onClick={() => setIsCreateModalOpen(true)}
            className="bg-gradient-to-r from-blue-600 via-blue-700 to-purple-600 text-white px-6 py-3 rounded-2xl hover:from-blue-700 hover:via-blue-800 hover:to-purple-700 transition-all duration-300 flex items-center space-x-3 shadow-xl shadow-blue-500/25 group"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.4, duration: 0.5 }}
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <motion.div
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="group-hover:animate-none"
            >
              <Sparkles size={20} />
            </motion.div>
            <span className="font-semibold">创建应用</span>
          </motion.button>
        </motion.div>

        {/* Search and Filters */}
        <motion.div 
          className="flex items-center space-x-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.6 }}
        >
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400" size={20} />
            <motion.input
              type="text"
              placeholder="搜索应用名称或描述"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-12 pr-4 py-3 border border-slate-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all duration-300 bg-white/80 backdrop-blur-sm shadow-sm"
              whileFocus={{ scale: 1.02 }}
            />
          </div>
          
          <div className="flex items-center space-x-3">
            <Filter className="text-slate-400" size={20} />
            <motion.select
              value={publishFilter}
              onChange={(e) => setPublishFilter(e.target.value as 'all' | 'published' | 'unpublished')}
              className="px-4 py-3 border border-slate-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all duration-300 bg-white/80 backdrop-blur-sm shadow-sm"
              whileFocus={{ scale: 1.02 }}
            >
              <option value="all">全部状态</option>
              <option value="published">已发布</option>
              <option value="unpublished">未发布</option>
            </motion.select>
          </div>
        </motion.div>

        {/* Applications Grid */}
        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          initial="hidden"
          animate="visible"
          variants={{
            hidden: { opacity: 0 },
            visible: {
              opacity: 1,
              transition: {
                staggerChildren: 0.1
              }
            }
          }}
        >
          {filteredApplications.map((app, index) => (
            <AIApplicationCard 
              key={app.id} 
              application={app} 
              index={index} 
              onEdit={onEditApplication}
            />
          ))}
        </motion.div>

        {/* Empty State */}
        {filteredApplications.length === 0 && (
          <motion.div 
            className="text-center py-16"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            <motion.div 
              className="w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-100 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg"
              animate={{ 
                rotate: [0, 5, -5, 0],
                scale: [1, 1.05, 1]
              }}
              transition={{ 
                duration: 4, 
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <Search className="text-blue-500" size={32} />
            </motion.div>
            <h3 className="text-2xl font-bold text-slate-900 mb-3">没有找到应用</h3>
            <p className="text-slate-600 mb-6 text-lg">尝试调整搜索条件或创建新的应用</p>
            <motion.button
              onClick={() => setIsCreateModalOpen(true)}
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-2xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 font-semibold shadow-lg shadow-blue-500/25"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
            >
              创建第一个应用
            </motion.button>
          </motion.div>
        )}
      </div>

      <CreateApplicationModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateApplication}
      />
    </div>
  );
};