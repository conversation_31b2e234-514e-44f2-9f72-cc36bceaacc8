/**
 * Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import styled from 'styled-components';

export const PasteIcon = styled.div`
  position: absolute;
  width: 15px;
  height: 15px;
  color: #3370ff;
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const Wrap = styled.div`
  position: relative;
  width: 6px;
  height: 6px;
  background-color: rgb(143, 149, 158);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  svg: {
    transform: scale(0.7);
  }
`;
