{"name": "@flowgram.ai/demo-vite", "version": "0.1.0", "description": "", "keywords": [], "license": "MIT", "main": "./src/index.tsx", "files": ["src/", ".eslintrc.js", ".giti<PERSON>re", "index.html", "package.json", "vite.config.js", "tsconfig.json"], "scripts": {"dev": "vite", "start": "vite", "build": "exit 0", "clean": "<PERSON><PERSON><PERSON> dist", "build:production": "vite build", "lint": "eslint ./src --cache", "lint:fix": "eslint ./src --fix", "test": "exit", "test:cov": "exit", "watch": "exit 0", "preview": "vite preview"}, "dependencies": {"@flowgram.ai/free-layout-editor": "workspace:*", "@flowgram.ai/free-snap-plugin": "workspace:*", "@flowgram.ai/minimap-plugin": "workspace:*", "react": "^18", "react-dom": "^18"}, "devDependencies": {"@flowgram.ai/ts-config": "workspace:*", "@flowgram.ai/eslint-config": "workspace:*", "@vitejs/plugin-react": "^4.4.1", "@types/lodash-es": "^4.17.12", "@types/node": "^18", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8.54.0", "cross-env": "~7.0.3", "globals": "^15.11.0", "less": "^4.1.2", "vite": "^6.3.5"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}}