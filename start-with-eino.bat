@echo off
echo 🚀 启动 AgentIn Demo with Eino Backend

REM 检查 Go 是否安装
where go >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Go 未安装，请先安装 Go 1.21+
    pause
    exit /b 1
)

REM 检查 Node.js 是否安装
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js
    pause
    exit /b 1
)

REM 创建日志目录
if not exist logs mkdir logs

echo 📦 安装前端依赖...
call npm install

echo 📦 安装 Eino 后端依赖...
cd eino-backend
go mod tidy
cd ..

echo 🔧 设置环境变量...
set REACT_APP_USE_EINO_BACKEND=true

echo 🚀 启动 Eino 后端服务器...
cd eino-backend
start /b go run main.go > ..\logs\eino-backend.log 2>&1
cd ..

echo ⏳ 等待后端服务器启动...
timeout /t 3 /nobreak >nul

echo 🚀 启动前端开发服务器...
start /b npm run dev

echo.
echo 🎉 启动完成！
echo 📊 前端地址: http://localhost:5173
echo 🔧 后端地址: http://localhost:8080
echo 📝 后端日志: logs\eino-backend.log
echo.
echo 按任意键停止所有服务...
pause >nul

echo 🛑 正在停止服务...
taskkill /f /im go.exe >nul 2>nul
taskkill /f /im node.exe >nul 2>nul
echo ✅ 所有服务已停止
