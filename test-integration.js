// 测试前后端集成的脚本
import fetch from 'node-fetch';

async function testEinoIntegration() {
  const API_BASE = 'http://localhost:8082/api';
  
  console.log('🧪 开始测试 Eino 集成...\n');
  
  try {
    // 1. 测试健康检查
    console.log('1️⃣ 测试健康检查...');
    const healthResponse = await fetch(`${API_BASE}/health`);
    const healthData = await healthResponse.json();
    console.log('✅ 健康检查成功:', healthData.message);
    console.log('   后端类型:', healthData.backend);
    console.log('   时间戳:', healthData.timestamp);
    console.log('');
    
    // 2. 测试获取应用列表
    console.log('2️⃣ 测试获取应用列表...');
    const appsResponse = await fetch(`${API_BASE}/applications`);
    const appsData = await appsResponse.json();
    console.log('✅ 获取应用列表成功');
    console.log('   当前应用数量:', appsData.applications.length);
    console.log('');
    
    // 3. 测试创建应用
    console.log('3️⃣ 测试创建应用...');
    const createResponse = await fetch(`${API_BASE}/applications`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: '测试智能助手',
        description: '这是一个通过 Eino 集成创建的测试应用',
        avatar: '🤖'
      })
    });
    const createData = await createResponse.json();
    console.log('✅ 创建应用成功');
    console.log('   应用ID:', createData.application.id);
    console.log('   应用名称:', createData.application.name);
    console.log('   创建时间:', createData.application.lastEdited);
    console.log('');
    
    // 4. 测试聊天功能
    console.log('4️⃣ 测试聊天功能...');
    const chatResponse = await fetch(`${API_BASE}/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        applicationId: createData.application.id,
        message: '你好，这是一个测试消息！',
        history: []
      })
    });
    const chatData = await chatResponse.json();
    console.log('✅ 聊天功能成功');
    console.log('   消息ID:', chatData.message.id);
    console.log('   回复类型:', chatData.message.type);
    console.log('   回复内容:', chatData.message.content.substring(0, 100) + '...');
    console.log('');
    
    // 5. 再次获取应用列表验证
    console.log('5️⃣ 验证应用已保存...');
    const finalAppsResponse = await fetch(`${API_BASE}/applications`);
    const finalAppsData = await finalAppsResponse.json();
    console.log('✅ 验证成功');
    console.log('   最终应用数量:', finalAppsData.applications.length);
    console.log('   新应用已保存:', finalAppsData.applications.some(app => app.id === createData.application.id));
    console.log('');
    
    console.log('🎉 所有测试通过！Eino 集成工作正常！');
    console.log('');
    console.log('📋 测试总结:');
    console.log('   ✅ 后端健康检查');
    console.log('   ✅ 应用列表获取');
    console.log('   ✅ 应用创建功能');
    console.log('   ✅ 聊天对话功能');
    console.log('   ✅ 数据持久化');
    console.log('');
    console.log('🌐 现在可以在浏览器中访问 http://localhost:5173 测试前端界面！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.log('');
    console.log('🔧 故障排除建议:');
    console.log('   1. 确认后端服务器正在运行 (http://localhost:8082)');
    console.log('   2. 检查网络连接');
    console.log('   3. 查看后端日志输出');
  }
}

// 运行测试
testEinoIntegration();
