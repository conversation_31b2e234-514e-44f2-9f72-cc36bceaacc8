# Theme Config

Theme config is located under `themeConfig` in the `doc` param. For example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  themeConfig: {
    // ...
  },
});
```

## nav

- Type: `Array`
- Default: `[]`

The `nav` configuration is an array of `NavItem` with the following types:

```ts
interface NavItem {
  // Navbar text
  text: string;
  // Navbar link
  link: '/';
  // Activation rules for navbar links
  activeMatch: '^/$|^/';
  // svg tag string or image URL(optional)
  tag?: string;
}
```

`activeMatch` is used to match the current route, when the route matches the `activeMatch` rule, the nav item will be highlighted. By default, `activeMatch` is the `link` of the nav item.

For example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  themeConfig: {
    nav: [
      {
        text: 'Home',
        link: '/',
      },
      {
        text: 'Guide',
        link: '/guide/',
      },
    ],
  },
});
```

Of course, multi-level menus can also be configured in the `nav` array with the following types:

```ts
interface NavGroup {
  text: string;
  // submenu
  items: NavItem[];
  // svg tag string or image URL(optional)
  tag?: string;
}
```

For example the following configuration:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  themeConfig: {
    nav: [
      {
        text: 'Home',
        link: '/',
      },
      {
        text: 'Guide',
        items: [
          {
            text: 'Getting Started',
            link: '/guide/getting-started',
          },
          {
            text: 'Advanced',
            link: '/guide/advanced',
          },
          // Also support sub group menu
          {
            text: 'Group',
            items: [
              {
                text: 'Personal',
                link: 'http://example.com/',
              },
              {
                text: 'Company',
                link: 'http://example.com/',
              },
            ],
          },
        ],
      },
    ],
  },
});
```

## sidebar

- Type: `Object`

The sidebar of the website. The config is an object with the following types:

```ts
// The key is the path of SidebarGroup
// value is an array of SidebarGroup
type Sidebar = Record<string, SidebarGroup[]>;

interface SidebarGroup {
  text: string;
  link?: string;
  items: SidebarItem[];
  // whether to be collapsible
  collapsible?: boolean;
  // Whether to be collapsed by default
  collapsed?: boolean;
  // svg tag string or image URL(optional)
  tag?: string;
}

// An object can be filled in, or a string can be filled in
// When filling in a string, it will be converted into an object internally, the string will be used as a link, and the text value will automatically take the title of the corresponding document
type SidebarItem =
  | {
      // sidebar text
      text: string;
      // sidebar link
      link: string;
      // svg tag string or image URL(optional)
      tag?: string;
    }
  | string;
```

For example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  themeConfig: {
    sidebar: {
      '/guide/': [
        {
          text: 'Getting Started',
          items: [
            // Fill in an object
            {
              text: 'Introduction',
              link: '/guide/getting-started/introduction',
            },
            {
              text: 'Installation',
              link: '/guide/getting-started/installation',
            },
          ],
        },
        {
          text: 'Advanced',
          items: [
            // Fill in the link string directly
            '/guide/advanced/customization',
            '/guide/advanced/markdown',
          ],
        },
      ],
    },
  },
});
```

## footer

- Type: `Object`
- Default: `{}`

The footer of the home page.

The `footer` config is an object of `Footer`, which has the following types:

```ts
export interface Footer {
  message?: string;
  copyright?: string;
}
```

`message` is a string that can contain HTML content. This string will be inserted into the footer using `dangerouslySetInnerHTML`, allowing you to pass in HTML template tags to design your footer.

For example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  themeConfig: {
    footer: {
      message:
        '<p>This is a footer with a <a href="https://example.com">link</a> and <strong>bold text</strong></p>',
    },
  },
});
```

## outlineTitle

- Type: `string`
- Default: 'ON THIS PAGE'

Configure the title of the outline in the outline panel.

For example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  themeConfig: {
    outlineTitle: 'Outline',
  },
});
```

## lastUpdated

- Type: `boolean`
- Default: `false`

Whether to display the last update time, it is not displayed by default.

For example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  themeConfig: {
    lastUpdated: true,
  },
});
```

## lastUpdatedText

- Type: `string`
- Default: `Last Updated`

The text of the last update time.

For example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  themeConfig: {
    lastUpdatedText: 'Last Updated',
  },
});
```

## prevPageText

- Type: `string`
- Default: `Previous Page`

The text of the previous page. for example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  themeConfig: {
    prevPageText: 'Previous Page',
  },
});
```

## searchPlaceholderText

- Type: `string`
- Default: `Search Docs`

The placeholder text of the search box. For example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  themeConfig: {
    searchPlaceholderText: 'Search Docs',
  },
});
```

## searchNoResultsText

- Type: `string`
- Default: `No results for`

The text of no search result. For example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  themeConfig: {
    searchNoResultsText: 'No results for',
  },
});
```

## searchSuggestedQueryText

- Type: `string`
- Default: `Please try again with a different keyword`

The text of suggested query text when no search result. For example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  themeConfig: {
    searchSuggestedQueryText: 'Please search again',
  },
});
```

## overview

- Type: `Object`

The config of overview page/component. The config is an object with the following types:

```ts
interface FilterConfig {
  filterNameText?: string;
  filterPlaceholderText?: string;
  filterNoResultText?: string;
}
```

For example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  themeConfig: {
    overview: {
      filterNameText: 'Filter',
      filterPlaceholderText: 'Enter keyword',
      filterNoResultText: 'No matching API found',
    },
  },
});
```

## socialLinks

- Type: `Array`
- Default: `[]`

You can add related links through the following config, such as `github` links, `x` links, etc.
Related links support four modes: `link mode` `text mode` `image mode` `dom mode`, for example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  themeConfig: {
    socialLinks: [
      {
        icon: 'github',
        mode: 'link',
        content: 'https://github.com/sanyuan0704/island.js',
      },
      {
        icon: 'wechat',
        mode: 'text',
        content: 'wechat: foo',
      },
      {
        icon: 'qq',
        mode: 'img',
        content: '/qrcode.png',
      },
      {
        icon: 'github',
        mode: 'dom',
        content:
          '<img loading="lazy" src="https://lf3-static.bytednsdoc.com/obj/eden-cn/rjhwzy/ljhwZthlaukjlkulzlp/rspress/rspress-navbar-logo-0904.png" alt="logo" id="logo" class="mr-4 rspress-logo dark:hidden">',
      },
    ],
  },
});
```

- When in `link` mode, click the icon to jump to the link.
- When in `text` mode, when the mouse moves over the icon, a pop-up box will be displayed, and the content of the pop-up box is the entered text
- When in the `img` mode, moving the mouse over the icon will display a bullet box, and the content of the bullet box is the specified picture. It should be noted that the picture needs to be placed in the `public` directory.
- When in dom mode, html to render can be passed directly into the content field. Use '' for wrapping

Related links support the following types of images, which can be selected through the icon attribute:

```ts
export type SocialLinkIcon =
  | 'lark'
  | 'discord'
  | 'facebook'
  | 'github'
  | 'instagram'
  | 'linkedin'
  | 'slack'
  | 'x'
  | 'twitter'
  | 'youtube'
  | 'wechat'
  | 'qq'
  | 'juejin'
  | 'zhihu'
  | 'bilibili'
  | 'weibo'
  | 'gitlab'
  | 'X'
  | { svg: string };
```

If you need to customize the icon, you can pass in an object with `svg attribute`, and the value of svg is the content of the custom icon, for example:

```js
import { defineConfig } from 'rspress/config';

export default defineConfig({
  themeConfig: {
    socialLinks: [
      {
        icon: {
          svg: '<svg>foo</svg>',
        },
        mode: 'link',
        content: 'https://github.com/',
      },
    ],
  },
});
```

## nextPageText

- Type: `string`
- Default: `Next Page`

Text for the next page. for example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  themeConfig: {
    nextPageText: 'Next Page',
  },
});
```

## locales

- Type: `Array<LocaleConfig>`
- Default: `undefined`

I18n config. This config is an array, and every item of it is `LocaleConfig`, and the types are as follows:

```ts
export interface LocaleConfig {
  /**
   * General locale config for site, which will have a higher priority than `locales`
   */
  // language name
  lang?: string;
  // HTML title, takes precedence over `themeConfig.title
  title?: string;
  // HTML description, takes precedence over `themeConfig.description`
  description?: string;
  // Display text for the corresponding language
  label: string;
  /**
   * Locale config for theme.
   */
  // Right outline title
  outlineTitle?: string;
  // Whether to display the outline title
  outline?: boolean;
  // Whether to display the last update time
  lastUpdated?: boolean;
  // Last update time text
  lastUpdatedText?: string;
  // Previous text
  prevPageText?: string;
  // Next page text
  nextPageText?: string;
  // Search box placeholder text
  searchPlaceholderText?: string;
  // The text of no search result
  searchNoResultsText?: string;
  // The text of suggested query text when no search result
  searchSuggestedQueryText?: string;
}
```

`LocaleConfig` contains many of the same configuration options as the theme config, but the former will have a higher priority.

## darkMode

- Type: `boolean`
- Default: `true`

Whether a Dark/Light mode toggle button appears. for example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  themeConfig: {
    darkMode: true,
  },
});
```

You can also specify the default theme mode through inject global variable into html template, for example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  builderConfig: {
    html: {
      tags: [
        {
          tag: 'script',
          // Specify the default theme mode, which can be `dark` or `light`
          children: "window.RSPRESS_THEME = 'dark';",
        },
      ],
    },
  },
});
```

## hideNavbar

- Type: `"always" | "auto" | "never"`
- Default: `never`

Control the behavior of the hidden navigation bar. By default, the navigation bar will always display. You can set it to `auto` to **automatically hide when the page scrolls down**, or set it to `always` to hidden it all the time.

For example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  themeConfig: {
    hideNavbar: 'auto',
  },
});
```

## enableContentAnimation

- Type: `boolean`
- Default: `false`

Whether there is animation effect when switching between pages. It is implemented with [View Transition API](https://developer.mozilla.org/docs/Web/API/View_Transitions_API). For example:

> The animation is not configurable for now.

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  themeConfig: {
    enableContentAnimation: true,
  },
});
```

## enableAppearanceAnimation

- Type: `boolean`
- Default: `false`

Whether there is animation effect when switching between light and dark theme. It is implemented with [View Transition API](https://developer.mozilla.org/docs/Web/API/View_Transitions_API). For example:

> The animation is not configurable for now.

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  themeConfig: {
    enableAppearanceAnimation: true,
  },
});
```

## search

- Type: `boolean`
- Default: `true`

Whether to display the search box. For example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  themeConfig: {
    search: false,
  },
});
```

## sourceCodeText

- Type: `string`
- Default: `Source`

The text of the source code button. For example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  themeConfig: {
    sourceCodeText: 'Source',
  },
});
```

## enableScrollToTop

- Type: `boolean`
- Default: `false`

Enable scroll to top button on documentation. For example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  themeConfig: {
    enableScrollToTop: true,
  },
});
```

## localeRedirect

- Type: `'auto' | 'never'`
- Default: `'auto'`

Whether to redirect to the locale closest to `window.navigator.language` when the user visits the site, the default is `auto`, which means that the user will be redirected on the first visit. If you set it to `never`, the user will not be redirected. For example:

```ts title="rspress.config.ts"
import { defineConfig } from 'rspress/config';

export default defineConfig({
  themeConfig: {
    localeRedirect: 'never',
  },
});
```
