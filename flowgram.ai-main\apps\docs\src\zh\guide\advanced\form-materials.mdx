import { PackageManagerTabs } from '@theme';
import { MaterialDisplay } from '../../../../components/materials';

# 官方表单物料

## 如何使用？

### 通过包引用使用

官方表单物料可以直接通过包引用使用：

<PackageManagerTabs command="install @flowgram.ai/form-materials" />

```tsx
import { JsonSchemaEditor } from '@flowgram.ai/form-materials'
```


### 通过 CLI 添加物料源代码使用


如果业务对组件有定制诉求（如：更改文案、样式、业务逻辑），推荐 **通过 CLI 将物料源代码添加到项目中进行定制**：

```bash
npx @flowgram.ai/form-materials@latest
```

运行后 CLI 会提示用户选择要添加到项目中的物料:

```console
? Select one material to add: (Use arrow keys)
❯ components/json-schema-editor
  components/type-selector
  components/variable-selector
```

使用者也可以直接在 CLI 中添加指定物料的源代码:

```bash
npx @flowgram.ai/form-materials@latest components/json-schema-editor
```

CLI 运行成功后，相关物料会自动添加到当前项目下的 `src/form-materials` 目录下

:::warning 注意事项

1. 官方物料目前底层基于 [Semi Design](https://semi.design/) 实现，业务如果有底层组件库的诉求，可以通过 CLI 复制源码进行替换
2. 一些物料会依赖一些第三方 npm 库，这些库会在 CLI 运行时自动安装
3. 一些物料会依赖另外一些官方物料，这些被依赖的物料源代码在 CLI 运行时会一起被添加到项目中去

:::

## 当前支持的 Component 物料

### TypeSelector

<MaterialDisplay
  imgs={['/materials/type-selector.png']}
  filePath="components/type-selector/index.tsx"
  exportName="TypeSelector"
>
  TypeSelector 用于变量类型选择
</MaterialDisplay>


### VariableSelector

<MaterialDisplay
  imgs={['/materials/variable-selector.png']}
  filePath="components/variable-selector/index.tsx"
  exportName="VariableSelector"
>
  VariableSelector 用于展示变量树，并从变量树中选择单个变量
</MaterialDisplay>


### JsonSchemaEditor

<MaterialDisplay
  imgs={['/materials/json-schema-editor.png']}
  filePath="components/json-schema-editor/index.tsx"
  exportName="JsonSchemaEditor"
>
  JsonSchemaEditor 用于可视化编辑 [JsonSchema](https://json-schema.org/)

  常用于可视化配置节点的输出变量
</MaterialDisplay>


### DynamicValueInput

<MaterialDisplay
  imgs={['/materials/dynamic-value-input.png']}
  filePath="components/dynamic-value-input/index.tsx"
  exportName="DynamicValueInput"
>
  DynamicValueInput 用于值（常量值 + 变量值）的配置
</MaterialDisplay>


### ConditionRow

<MaterialDisplay
  imgs={[{ src: '/materials/condition-row.png', caption: '第一个条件为 query 变量包含 Hello Flow，第二个条件为 enable 变量为 true' }]}
  filePath="components/condition-row/index.tsx"
  exportName="ConditionRow"
>
  ConditionRow 用于 **一行** 条件判断的配置
</MaterialDisplay>

### PromptEditorWithVariables

<MaterialDisplay
  imgs={[{ src: '/materials/prompt-editor-with-variables.png', caption: 'LLM_3 和 LLM_4 的提示词中引用了循环的批处理变量' }]}
  filePath="components/prompt-editor-with-variables/index.tsx"
  exportName="PromptEditorWithVariables"
>
  PromptEditorWithVariables 用于支持变量配置的 Prompt 编辑器。

  下面是一个 Prompt 编辑器的配置示例，其中 `query` 变量为字符串类型，`enable` 变量为布尔类型：
  ```typescript
  {
    type: "template",
    content: "#User Input:\nquery:{{start_0.query}}\nenable:{{start_0.enable}}"
  }
  ```
</MaterialDisplay>

## 当前支持的 Effect 物料

### provideBatchInput

<MaterialDisplay
  imgs={[{ src: '/materials/provide-batch-input.png', caption: 'item 的类型会自动根据前置类型联动推导' }]}
  filePath="effects/provide-batch-input/index.ts"
  exportName="provideBatchInputEffect"
>
  provideBatchInputEffect 用于配置循环批处理输入推导，会根据输入自动推导出两个变量：
  - item：根据输入变量数组类型自动推导，代表循环的每一项
  - index：数字类型，代表循环第几次
</MaterialDisplay>

### autoRenameRef

<MaterialDisplay
  imgs={[{ src: '/materials/auto-rename-ref.gif', caption: 'query 变量名变化时，自动重命名下游 inputs 中的引用' }]}
  filePath="effects/auto-rename-ref/index.ts"
  exportName="autoRenameRefEffect"
>
  当前置输出变量名发生变化时:
  - 表单项中所有的对该变量的引用，自动重命名
</MaterialDisplay>
