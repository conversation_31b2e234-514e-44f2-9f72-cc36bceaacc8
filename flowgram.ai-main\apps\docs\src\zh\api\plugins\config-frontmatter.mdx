# Front Matter Config

## title

- Type: `string`

The title of the page. By default, the page's h1 heading will be used as the title of the HTML document. But if you want to use a different title, you can use Front Matter to specify the title of the page. For example:

```yaml
---
title: My Home Page
---
```

## description

- Type: `string`

A custom description for the page. For example:

```yaml
---
description: This is my custom description for this page.
---
```

## pageType

- Type: `'home' | 'doc' | 'custom' | 'blank' | '404'`
- Default: `'doc'`

The type of the page. By default, the page type is `doc`. But if you want to use a different page type, you can use the Front Matter field `pageType` to specify the page type. E.g:

```yaml
---
pageType: home
---
```

The meaning of each `pageType` config is as follows:

## titleSuffix

- Type: `string`

Set the suffix of the page title. When `titleSuffix` is not set, the site's [title](/api/config/config-basic#title) is used as the suffix by default.

```yaml
---
titleSuffix: 'Rspack-based Static Site Generator'
---
```

The default separator between the title and the suffix is `-`, you can also use `|` for separation:

```yaml
---
titleSuffix: '| Rspack-based Static Site Generator'
---
```

## head

- Type: `[string, Record<string, string>][]`

Specify extra head tags to be injected for the current page. Will be appended after head tags injected by site-level config.

For example, you can use these headers to specify custom meta tags for [Open Graph](https://ogp.me/).

```yaml
---
head:
  - - meta
    - property: og:title
      content: The Rock
  - - meta
    - property: og:url
      content: https://www.imdb.com/title/tt0117500/
  - - meta
    - property: og:image
      content: https://ia.media-imdb.com/images/rock.jpg
# - - [htmlTag]
#   - [attributeName]: [attributeValue]
#     [attributeName]: [attributeValue]
---
```

::: tip Note

Make sure to correctly define the header tag names and their attribute names.

For tags and attribute names that contain a hyphen (`-`), use the camelCase format.
For example, `http-equiv="refresh"` should be defined as `httpEquiv: refresh`.

This is because under the hood, headers are handled by React and react-helmet-async.

:::

## hero

- Type: `Object`

The hero config for the home page. It has the following types:

```ts
interface Hero {
  name: string;
  text: string;
  tagline: string;
  image?: {
    src: string | { dark: string; light: string };
    alt: string;
    /**
     * `srcset` and `sizes` are attributes of `<img>` tag. Please refer to https://mdn.io/srcset for the usage.
     * When the value is an array, rspress will join array members with commas.
     **/
    srcset?: string | string[];
    sizes?: string | string[];
  };
  actions: {
    text: string;
    link: string;
    theme: 'brand' | 'alt';
  }[];
}
```

For example, you can use the following Front Matter to specify a page's hero config:

```yaml
---
pageType: home

hero:
  name: Rspress
  text: A Documentation Solution
  tagline: A modern documentation development technology stack
  actions:
    - theme: brand
      text: Introduction
      link: /en/guide/introduction
    - theme: alt
      text: Quick Start
      link: /en/guide/getting-started
---
```

When setting `hero.text`, you can use the `|` symbol in YAML to manually control line breaks:

```yaml
---
pageType: home

hero:
  name: Rspress
  text: |
    A Documentation
    Solution
```

Or you can use `HTML` to specify the hero config for the page:

```yaml
---
pageType: home

hero:
  name: <span class="hero-name">Rspress</span>
  text: <span class="hero-text">A Documentation Solution</span>
  tagline: <span class="hero-tagline">A modern documentation development technology stack</span>
  actions:
    - theme: brand
      text: <span class="hero-actions-text">Introduction</span>
      link: /zh/guide/introduction
    - theme: alt
      text: <span class="hero-actions-text">Quick Start</span>
      link: /zh/guide/getting-started
---
```

## features

- Type: `Array`
- Default: `[]`

features config of the `home` page. It has the following types:

```ts
interface Feature {
  title: string;
  details: string;
  icon: string;
  // The length of the card grid, currently only support[3, 4, 6]
  span?: number;
  // The link of the feature, not required.
  link?: string;
}

export type Features = Feature[];
```

For example, you could use the following to specify the features configuration for the `home` page:

```yaml
---
pageType: home

features:
  - title: 'MDX Support'
    details: MDX is a powerful way to write content. You can use React components in Markdown.
    icon: 📦
  - title: 'Feature Rich'
    details: Out of box support for i18n, full-text search etc.
    icon: 🎨
  - title: 'Customizable'
    details: You can customize the theme ui and the build process.
    icon: 🚀
---
```

## sidebar

Whether to show the sidebar on the left. By default, the `doc` page will display the sidebar on the left. If you want to hide the sidebar on the left, you can use the following Front Matter config:

```yaml
---
sidebar: false
---
```

## outline

Whether to display the outline column on the right. By default, the `doc` page displays the outline column on the right. You can hide the outline column with the following config:

```yaml
---
outline: false
---
```

## footer

Whether to display the components at the bottom of the document (such as previous/next page). By default, the `doc` page will display the footer at the bottom. You can hide the footer with the following config:

```yaml
---
footer: false
---
```

## navbar

Whether to hide the top navigation bar. You can hide the top nav bar with the following config:

```yaml
---
navbar: true
---
```

## overviewHeaders

- Type: `number[]`
- Default: `[2]`

The headers shown in the overview page. By default, the displayed header is h2. But if you want to display different headers, you can specify it using the `overviewHeaders` Front Matter field. For example:

```yaml
---
overviewHeaders: []
---
```

Or

```yaml
---
overviewHeaders: [2, 3]
---
```

## context

- Type: `string`

After configuration, the `data-context` attribute will be added to the DOM node when the sidebar is generated, and the value is the configured value.

```yaml title="foo.mdx"
---
context: 'context-foo'
---
```

```yaml title="bar.mdx"
---
context: 'context-bar'
---
```

The DOM structure of the final generated sidebar is abbreviated as follows:

```html
<div class="rspress-sidebar-group">
  <div className="rspress-sidebar-item" data-context="context-foo"></div>
  <div className="rspress-sidebar-item" data-context="context-bar"></div>
</div>
```
