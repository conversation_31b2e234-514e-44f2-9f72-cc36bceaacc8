---
outline: false
---


# 最佳实践

import { FixedFeatureOverview } from '../../../../components';

<FixedFeatureOverview />

## 安装

```bash
npx @flowgram.ai/create-app@latest fixed-layout
```

## 源码

https://github.com/bytedance/flowgram.ai/tree/main/apps/demo-fixed-layout

## 功能介绍

<table className="rs-table">
  <tr>
    <td>缩略图</td>
    <td>
      <div className="rs-center">
        <img loading="lazy" src="/fixed-layout/minimap.gif"  />
      </div>
    </td>
  </tr>
  <tr>
    <td>撤销/重做</td>
    <td>
      <div className="rs-center">
        <img loading="lazy" src="/fixed-layout/redo-undo.gif"  />
      </div>
    </td>
  </tr>
  <tr>
    <td>复制/粘贴(支持快捷键)</td>
    <td>
      <div className="rs-center">
        <img loading="lazy" src="/fixed-layout/copypaste.gif"  />
      </div>
    </td>
  </tr>
  <tr>
    <td>
      <div>
        <div>框选 + 拖拽</div>
      </div>
    </td>
    <td>
      <div className="rs-center">
        <div className="rs-center">
          <img loading="lazy" src="/fixed-layout/dragdrop.gif"  />
        </div>
      </div>
    </td>
  </tr>
  <tr>
    <td>
      <div>水平/垂直布局切换</div>
    </td>
    <td>
      <div className="rs-center">
        <img loading="lazy" src="/fixed-layout/layout-change.gif"  />
      </div>
    </td>
  </tr>
  <tr>
    <td>
      <div>分支折叠</div>
    </td>
    <td>
      <div className="rs-center">
        <img loading="lazy" src="/fixed-layout/fold.gif"  />
      </div>
    </td>
  </tr>
  <tr>
    <td>
      <div>分组</div>
    </td>
    <td>
      <div className="rs-center">
        <img loading="lazy" src="/fixed-layout/group.gif"  />
      </div>
    </td>
  </tr>
</table>
