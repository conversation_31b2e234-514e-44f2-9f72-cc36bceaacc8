{"extends": "@flowgram.ai/ts-config/tsconfig.flow.path.json", "compilerOptions": {"target": "ES2020", "lib": ["DOM", "ES2020"], "types": ["node"], "module": "ESNext", "jsx": "react-jsx", "noEmit": true, "strict": true, "skipLibCheck": true, "isolatedModules": true, "resolveJsonModule": true, "useDefineForClassFields": true, "allowImportingTsExtensions": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src", "rspress.config.ts", "src/zh/playground/canvas-engine/components", "components", "components", "scripts"], "mdx": {"checkMdx": true}}