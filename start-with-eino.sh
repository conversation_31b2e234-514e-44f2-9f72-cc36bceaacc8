#!/bin/bash

# 启动脚本：同时运行前端和 Eino 后端
echo "🚀 启动 AgentIn Demo with <PERSON>o Backend"

# 检查是否安装了 Go
if ! command -v go &> /dev/null; then
    echo "❌ Go 未安装，请先安装 Go 1.21+"
    exit 1
fi

# 检查是否安装了 Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

# 创建日志目录
mkdir -p logs

echo "📦 安装前端依赖..."
npm install

echo "📦 安装 Eino 后端依赖..."
cd eino-backend
go mod tidy
cd ..

echo "🔧 设置环境变量..."
export REACT_APP_USE_EINO_BACKEND=true

# 启动后端服务器
echo "🚀 启动 Eino 后端服务器..."
cd eino-backend
go run main.go > ../logs/eino-backend.log 2>&1 &
BACKEND_PID=$!
cd ..

# 等待后端启动
echo "⏳ 等待后端服务器启动..."
sleep 3

# 检查后端是否启动成功
if curl -s http://localhost:8080/api/health > /dev/null 2>&1; then
    echo "✅ Eino 后端服务器启动成功 (PID: $BACKEND_PID)"
else
    echo "⚠️  后端服务器可能未完全启动，但继续启动前端..."
fi

# 启动前端开发服务器
echo "🚀 启动前端开发服务器..."
npm run dev &
FRONTEND_PID=$!

echo ""
echo "🎉 启动完成！"
echo "📊 前端地址: http://localhost:5173"
echo "🔧 后端地址: http://localhost:8080"
echo "📝 后端日志: logs/eino-backend.log"
echo ""
echo "按 Ctrl+C 停止所有服务"

# 创建清理函数
cleanup() {
    echo ""
    echo "🛑 正在停止服务..."
    
    if kill -0 $BACKEND_PID 2>/dev/null; then
        echo "🔧 停止 Eino 后端服务器..."
        kill $BACKEND_PID
    fi
    
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        echo "📊 停止前端开发服务器..."
        kill $FRONTEND_PID
    fi
    
    echo "✅ 所有服务已停止"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 等待进程
wait
