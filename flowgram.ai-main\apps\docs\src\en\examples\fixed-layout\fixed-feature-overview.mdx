---
outline: false
---


# Best Practices

import { FixedFeatureOverview } from '../../../../components';

<FixedFeatureOverview />

## Installation

```bash
npx @flowgram.ai/create-app@latest fixed-layout
```

## Source Code

https://github.com/bytedance/flowgram.ai/tree/main/apps/demo-fixed-layout

## Feature Introduction

<table className="rs-table">
  <tr>
    <td>Minimap</td>
    <td>
      <div className="rs-center">
        <img loading="lazy" src="/fixed-layout/minimap.gif"  />
      </div>
    </td>
  </tr>
  <tr>
    <td>Undo/Redo</td>
    <td>
      <div className="rs-center">
        <img loading="lazy" src="/fixed-layout/redo-undo.gif"  />
      </div>
    </td>
  </tr>
  <tr>
    <td>Copy/Paste (Support Shortcut)</td>
    <td>
      <div className="rs-center">
        <img loading="lazy" src="/fixed-layout/copypaste.gif"  />
      </div>
    </td>
  </tr>
  <tr>
    <td>
      <div>
        <div>Box Selection + Drag</div>
      </div>
    </td>
    <td>
      <div className="rs-center">
        <div className="rs-center">
          <img loading="lazy" src="/fixed-layout/dragdrop.gif"  />
        </div>
      </div>
    </td>
  </tr>
  <tr>
    <td>
      <div>Horizontal/Vertical Layout Switch</div>
    </td>
    <td>
      <div className="rs-center">
        <img loading="lazy" src="/fixed-layout/layout-change.gif"  />
      </div>
    </td>
  </tr>
  <tr>
    <td>
      <div>Branch Fold</div>
    </td>
    <td>
      <div className="rs-center">
        <img loading="lazy" src="/fixed-layout/fold.gif"  />
      </div>
    </td>
  </tr>
  <tr>
    <td>
      <div>Group</div>
    </td>
    <td>
      <div className="rs-center">
        <img loading="lazy" src="/fixed-layout/group.gif"  />
      </div>
    </td>
  </tr>
</table>
