/**
 * Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

.panel-wrapper {
  position: relative;
  z-index: 9999;
}

.variable-panel-button {
  position: absolute;
  top: 0;
  right: 0;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  z-index: 1;

  &.close {
    width: 30px;
    height: 30px;
    top: 10px;
    right: 10px;
  }
}

.panel-container {
  width: 500px;
  border-radius: 5px;
  background-color: #fff;
  overflow: hidden;
  box-shadow: 4px 4px 4px rgba(0, 0, 0, 0.1);
  z-index: 30;

  :global(.semi-tabs-bar) {
    padding-left: 20px;
  }

  :global(.semi-tabs-content) {
    padding: 20px;
    height: 500px;
    overflow: auto;
  }
}
