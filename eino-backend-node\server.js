// Node.js 版本的 Eino 后端模拟
import express from 'express';
import cors from 'cors';

const app = express();
const PORT = 8081;

// 中间件
app.use(cors());
app.use(express.json());

// 模拟数据存储
let applications = new Map();
let applicationCounter = 1;

// 模拟 Eino Graph 处理
class MockEinoGraph {
  constructor(appName, appDesc) {
    this.appName = appName;
    this.appDesc = appDesc;
  }

  async invoke(input) {
    const userMessage = input.query;
    
    // 模拟 AI 处理逻辑
    const responses = [
      `我是${this.appName}，${this.appDesc}。关于您的问题"${userMessage}"，让我来为您详细解答...`,
      `作为${this.appName}，我理解您想了解"${userMessage}"。基于我的知识，我可以告诉您...`,
      `您好！我是专门的${this.appName}助手。对于"${userMessage}"这个问题，我的建议是...`,
      `感谢您使用${this.appName}！关于"${userMessage}"，让我从专业角度为您分析...`
    ];
    
    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
    
    // 模拟处理时间
    await new Promise(resolve => setTimeout(resolve, 800 + Math.random() * 1200));
    
    return {
      Role: 'assistant',
      Content: randomResponse
    };
  }
}

// API 路由

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', backend: 'Node.js Mock Eino', timestamp: new Date().toISOString() });
});

// 获取应用列表
app.get('/api/applications', (req, res) => {
  const apps = Array.from(applications.values());
  res.json({ applications: apps });
});

// 创建应用
app.post('/api/applications', (req, res) => {
  const { name, description, avatar } = req.body;
  
  if (!name || !description) {
    return res.status(400).json({ error: '应用名称和描述不能为空' });
  }
  
  const appId = `app_${Date.now()}_${applicationCounter++}`;
  const newApp = {
    id: appId,
    name,
    description,
    avatar: avatar || '🤖',
    isPublished: false,
    lastEdited: new Date().toISOString()
  };
  
  // 创建对应的 Mock Eino Graph
  const graph = new MockEinoGraph(name, description);
  applications.set(appId, { ...newApp, graph });
  
  console.log(`📱 创建新应用: ${name} (ID: ${appId})`);
  res.json({ application: newApp });
});

// 获取应用详情
app.get('/api/applications/:id', (req, res) => {
  const appId = req.params.id;
  const app = applications.get(appId);
  
  if (!app) {
    return res.status(404).json({ error: '应用不存在' });
  }
  
  // 不返回 graph 对象
  const { graph, ...appData } = app;
  res.json({ application: appData });
});

// 聊天接口
app.post('/api/chat', async (req, res) => {
  const { applicationId, message, history } = req.body;
  
  if (!applicationId || !message) {
    return res.status(400).json({ error: '应用ID和消息内容不能为空' });
  }
  
  const app = applications.get(applicationId);
  if (!app) {
    return res.status(404).json({ error: '应用不存在' });
  }
  
  try {
    console.log(`💬 ${app.name} 收到消息: ${message}`);
    
    // 使用 Mock Eino Graph 处理消息
    const result = await app.graph.invoke({ query: message });
    
    const response = {
      id: Date.now(),
      type: 'assistant',
      content: result.Content
    };
    
    console.log(`🤖 ${app.name} 回复: ${result.Content.substring(0, 50)}...`);
    res.json({ message: response });
    
  } catch (error) {
    console.error('聊天处理错误:', error);
    res.status(500).json({ error: '处理消息时发生错误' });
  }
});

// 流式聊天接口（未来扩展）
app.post('/api/chat/stream', async (req, res) => {
  const { applicationId, message } = req.body;
  
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  
  const app = applications.get(applicationId);
  if (!app) {
    res.write(`data: ${JSON.stringify({ error: '应用不存在' })}\n\n`);
    res.end();
    return;
  }
  
  try {
    const result = await app.graph.invoke({ query: message });
    const words = result.Content.split(' ');
    
    // 模拟流式输出
    for (let i = 0; i < words.length; i++) {
      const chunk = {
        id: Date.now(),
        type: 'assistant',
        content: words.slice(0, i + 1).join(' ')
      };
      
      res.write(`data: ${JSON.stringify(chunk)}\n\n`);
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    res.write('data: [DONE]\n\n');
    res.end();
    
  } catch (error) {
    res.write(`data: ${JSON.stringify({ error: '处理消息时发生错误' })}\n\n`);
    res.end();
  }
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  res.status(500).json({ error: '服务器内部错误' });
});

// 启动服务器
app.listen(PORT, () => {
  console.log('🚀 Mock Eino Backend 启动成功!');
  console.log(`📊 服务地址: http://localhost:${PORT}`);
  console.log(`🔧 API 文档: http://localhost:${PORT}/api/health`);
  console.log('');
  console.log('📱 支持的功能:');
  console.log('  ✅ 应用创建和管理');
  console.log('  ✅ 智能聊天对话');
  console.log('  ✅ 流式响应 (实验性)');
  console.log('  ✅ 完全兼容前端接口');
  console.log('');
  console.log('🎯 这是一个模拟 Eino 功能的 Node.js 后端');
  console.log('   在实际部署中，请使用真正的 Eino Go 后端');
});
