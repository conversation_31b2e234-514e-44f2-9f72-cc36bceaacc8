{"name": "@flowgram.ai/demo-fixed-layout-simple", "version": "0.1.0", "description": "", "keywords": [], "license": "MIT", "main": "./src/index.ts", "files": ["src/", ".eslintrc.js", ".giti<PERSON>re", "index.html", "package.json", "rsbuild.config.ts", "tsconfig.json"], "scripts": {"build": "exit 0", "build:fast": "exit 0", "build:watch": "exit 0", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "cross-env MODE=app NODE_ENV=development rsbuild dev --open", "lint": "eslint ./src --cache", "lint:fix": "eslint ./src --fix", "start": "cross-env NODE_ENV=development rsbuild dev --open", "test": "exit", "test:cov": "exit", "watch": "exit 0"}, "dependencies": {"@flowgram.ai/ts-config": "workspace:*", "@flowgram.ai/eslint-config": "workspace:*", "@douyinfe/semi-icons": "^2.80.0", "@douyinfe/semi-ui": "^2.80.0", "@flowgram.ai/fixed-layout-editor": "workspace:*", "@flowgram.ai/fixed-semi-materials": "workspace:*", "@flowgram.ai/minimap-plugin": "workspace:*", "nanoid": "^4.0.2", "react": "^18", "react-dom": "^18"}, "devDependencies": {"@rsbuild/core": "^1.2.16", "@rsbuild/plugin-react": "^1.1.1", "@types/node": "^18", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8.54.0", "cross-env": "~7.0.3"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}}