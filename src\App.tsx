import React, { useState } from 'react';
import { Sidebar } from './components/Sidebar';
import { SecondaryNavigation } from './components/SecondaryNavigation';

function App() {
  const [activeSection, setActiveSection] = useState('studio');
  const [activeSubSection, setActiveSubSection] = useState('ai-apps');

  const handleSectionChange = (section: string) => {
    setActiveSection(section);
    // Reset to first sub-section when changing main section
    const defaultSubSections = {
      studio: 'ai-apps',
      templates: 'official',
      wallet: 'balance',
      my: 'profile',
    };
    // Store section doesn't use sub-sections anymore
    if (section === 'store') {
      setActiveSubSection('');
    } else {
      setActiveSubSection(defaultSubSections[section as keyof typeof defaultSubSections] || 'ai-apps');
    }
  };

  const handleSubSectionChange = (subSection: string) => {
    setActiveSubSection(subSection);
  };

  return (
    <div className="flex h-screen bg-gray-100">
      <Sidebar
        activeSection={activeSection}
        onSectionChange={handleSectionChange}
      />

      {/* Only show SecondaryNavigation for sections that need it */}
      {activeSection !== 'store' && (
        <SecondaryNavigation
          activeSection={activeSection}
          activeSubSection={activeSubSection}
          onSubSectionChange={handleSubSectionChange}
        />
      )}

      {/* 临时内容区域 */}
      <div className="flex-1 p-8 bg-white">
        <h1 className="text-2xl font-bold text-gray-800 mb-4">当前选中: {activeSection}</h1>
        <p className="text-gray-600 mb-2">子部分: {activeSubSection}</p>
        <p className="text-gray-600">二级导航正常工作，内容区域待恢复</p>
      </div>
    </div>
  );
}

export default App;