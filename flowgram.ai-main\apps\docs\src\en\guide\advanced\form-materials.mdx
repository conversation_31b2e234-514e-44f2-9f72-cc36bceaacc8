import { PackageManagerTabs } from '@theme';
import { MaterialDisplay } from '../../../../components/materials';

# Official Form Materials

## How to Use?

### Via Package Reference

Official form materials can be used directly via package reference:

<PackageManagerTabs command="install @flowgram.ai/form-materials" />

```tsx
import { JsonSchemaEditor } from '@flowgram.ai/form-materials'
```


### Adding Material Source Code via CLI

If customization of components is required (e.g., changing text, styles, or business logic), it is recommended to **add the material source code to the project for customization via CLI**:

```bash
npx @flowgram.ai/form-materials@latest
```

After running, the CLI will prompt the user to select the material to add to the project:

```console
? Select one material to add: (Use arrow keys)
❯ components/json-schema-editor
  components/type-selector
  components/variable-selector
```

Users can also directly add the source code of a specified material via CLI:

```bash
npx @flowgram.ai/form-materials@latest components/json-schema-editor
```

After the CLI runs successfully, the relevant materials will be automatically added to the `src/form-materials` directory in the current project.

:::warning Notes

1. Official materials are currently implemented based on [Semi Design](https://semi.design/). If there is a need for a different underlying component library, the source code can be copied via CLI for replacement.
2. Some materials may depend on third-party npm libraries, which will be automatically installed during CLI execution.
3. Some materials may depend on other official materials. The source code of these dependent materials will also be added to the project during CLI execution.

:::

## Currently Supported Component Materials

### TypeSelector

<MaterialDisplay
  imgs={['/materials/type-selector.png']}
  filePath="components/type-selector/index.tsx"
  exportName="TypeSelector"
>
  TypeSelector is used for variable type selection.
</MaterialDisplay>


### VariableSelector

<MaterialDisplay
  imgs={['/materials/variable-selector.png']}
  filePath="components/variable-selector/index.tsx"
  exportName="VariableSelector"
>
  VariableSelector is used to display a variable tree and select a single variable from it.
</MaterialDisplay>


### JsonSchemaEditor

<MaterialDisplay
  imgs={['/materials/json-schema-editor.png']}
  filePath="components/json-schema-editor/index.tsx"
  exportName="JsonSchemaEditor"
>
  JsonSchemaEditor is used for visually editing [JsonSchema](https://json-schema.org/).

  Commonly used for visually configuring the output variables of nodes.
</MaterialDisplay>


### DynamicValueInput

<MaterialDisplay
  imgs={['/materials/dynamic-value-input.png']}
  filePath="components/dynamic-value-input/index.tsx"
  exportName="DynamicValueInput"
>
  DynamicValueInput is used for configuring values (constant values + variable values).
</MaterialDisplay>

### ConditionRow

<MaterialDisplay
  imgs={[{ src: '/materials/condition-row.png', caption: 'The first condition checks if the query variable contains Hello Flow, the second condition checks if the enable variable is true.' }]}
  filePath="components/condition-row/index.tsx"
  exportName="ConditionRow"
>
  ConditionRow is used for configuring a **single row** of condition judgment.
</MaterialDisplay>

### PromptEditorWithVariables

<MaterialDisplay
  imgs={[{ src: '/materials/prompt-editor-with-variables.png', caption: 'LLM_3 and LLM_4 use variables from batch item of Loop' }]}
  filePath="components/prompt-editor-with-variables/index.tsx"
  exportName="PromptEditorWithVariables"
>
  PromptEditorWithVariables is a Prompt editor that supports variable configuration.

  Below is a configuration example for the Prompt editor, where the `query` variable is of string type and the `enable` variable is of boolean type:
  ```typescript
  {
    type: "template",
    content: "#User Input:\nquery:{{start_0.query}}\nenable:{{start_0.enable}}"
  }
  ```
</MaterialDisplay>

## Currently Supported Effect Materials

### provideBatchInput

<MaterialDisplay
  imgs={[{ src: '/materials/provide-batch-input.png', caption: 'Type of item is automatically inferred based on preceding type.' }]}
  filePath="effects/provide-batch-input/index.ts"
  exportName="provideBatchInputEffect"
>
  provideBatchInputEffect is used for configuring loop batch input derivation. It automatically derives two variables based on the input:
  - item: Derived from the input variable array type, representing each item in the loop.
  - index: Numeric type, representing the iteration count.
</MaterialDisplay>

### autoRenameRef

<MaterialDisplay
  imgs={[{ src: '/materials/auto-rename-ref.gif', caption: 'When the query variable name changes, automatically rename references in downstream inputs.' }]}
  filePath="effects/auto-rename-ref/index.ts"
  exportName="autoRenameRefEffect"
>
  When the name of a preceding output variable changes:
  - All references to that variable in form items are automatically renamed.
</MaterialDisplay>
