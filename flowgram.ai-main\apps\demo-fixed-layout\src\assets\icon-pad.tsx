/**
 * Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

export function PadIcon() {
  return (
    <svg width="48" height="38" viewBox="0 0 48 38" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect
        x="1.83317"
        y="1.49998"
        width="44.3333"
        height="35"
        rx="3.5"
        stroke="currentColor"
        strokeOpacity="0.8"
        strokeWidth="2.33333"
      />
      <path
        d="M14.6665 30.6667H33.3332"
        stroke="currentColor"
        strokeOpacity="0.8"
        strokeWidth="2.33333"
        strokeLinecap="round"
      />
    </svg>
  );
}
