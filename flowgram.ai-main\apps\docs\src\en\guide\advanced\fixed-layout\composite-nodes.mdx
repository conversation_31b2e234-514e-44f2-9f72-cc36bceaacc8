# Composite nodes

Composite nodes are composed of multiple nodes and support custom lines, such as condition, loop, and TryCatch.

## Usage

```ts pure title="node-registries.ts"

import { FlowNodeRegistry  } from '@flowgram.ai/fixed-layout-editor';

/**
 * Node registration
 */
export const nodeRegistries: FlowNodeRegistry[] = [
  {
    type: 'yourCustomNodeType',
    extend: 'dynamicSplit',
  },
];

```

## Built-in composite nodes

<div className="rs-tip">
  <a className="rs-link" target="_blank" href="https://github.com/bytedance/flowgram.ai/tree/main/packages/canvas-engine/fixed-layout-core/src/activities">
    Source Code
  </a>
</div>

import { CompositeNodesPreview } from '../../../../../components';

<CompositeNodesPreview />
